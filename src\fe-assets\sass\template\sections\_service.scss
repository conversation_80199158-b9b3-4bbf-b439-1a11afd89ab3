/* Global Style ---------------------------------- */
.shape-icon {
  position: relative;
  z-index: 2;
  width: 90px;
  height: 85px;
  line-height: 85px;
  text-align: center;
  &:before {
    content: "";
    position: absolute;
    inset: 0;
    background-color: $smoke-color2;
    clip-path: path(
      "M4.76563 19.2144C-2.32686 32.07 -1.20075 48.6639 6.14105 61.3767C16.4024 79.1459 38.9816 89.016 58.6174 83.4451C78.2532 77.8741 92.5688 56.7417 89.6127 36.3982C84.2306 -0.647078 23.3991 -14.559 4.76563 19.2144Z"
    );
    z-index: -1;
    transition: 0.4s ease-in-out;
  }
  .dots {
    &:before,
    &:after {
      content: "";
      position: absolute;
      background-color: $theme-color;
      height: 24px;
      width: 23px;
      border-radius: 50%;
    }
    &:before {
      top: 0;
      right: 9px;
    }
    &:after {
      height: 12px;
      width: 11px;
      bottom: 0;
      left: 27px;
    }
  }
}
/* Service Featured ---------------------------------- */
.service-featured {
  text-align: center;
  position: relative;
  z-index: 2;
  &:before {
    content: "";
    height: 180px;
    width: 100%;
    background-color: $smoke-color;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    border-radius: 10px;
    z-index: -1;
    transition: 0.4s ease-in-out;
  }
  .shape-icon {
    margin: 0 auto 30px auto;
  }
  .icon-btn {
    border-radius: 99px;
    background-color: $theme-color;
    box-shadow: 0px 6px 20px rgba(0, 96, 255, 0.5);
    border: none;
    color: $white-color;
    position: absolute;
    bottom: -28px;
    left: calc(50% - 28px);
    &:hover {
      background-color: $white-color;
      color: $theme-color;
    }
  }
  &_content {
    background-color: $white-color;
    max-width: 312px;
    box-shadow: 0px 10px 30px rgba(8, 14, 28, 0.06);
    border-radius: 10px;
    margin-left: auto;
    margin-right: auto;
    padding: 40px 15px 28px 15px;
    margin-bottom: 28px;
  }
  &_text {
    max-width: 255px;
    margin: 0 auto 31px auto;
  }
}

/* Service Card ---------------------------------- */
.service-card {
  padding: 40px;
  position: relative;
  z-index: 2;
  box-shadow: 0px 10px 30px rgba(8, 14, 28, 0.06);
  background-color: $white-color;
  border-radius: 10px;
  overflow: hidden;
  transition: 0.4s ease-in-out;
  &:before,
  &:after {
    content: "";
    position: absolute;
    height: 110px;
    width: 110px;
    background-color: $theme-color;
    opacity: 0.6;
    border-radius: 50%;
    transition: 0.4s ease-in-out;
    z-index: -1;
  }
  &:before {
    bottom: -73px;
    right: -28px;
  }
  &:after {
    right: -73px;
    bottom: -28px;
  }
  .shape-icon {
    margin-bottom: 30px;
  }
  .box-title {
    a {
      &:hover {
        color: $smoke-color2;
      }
    }
  }
  .bg-shape {
    position: absolute;
    bottom: -200px;
    left: 0;
    width: 100%;
    opacity: 0;
    transition: 0.4s ease-in-out;
    pointer-events: none;
    img {
      width: 100%;
    }
  }
  &_number {
    position: absolute;
    top: 25px;
    right: 40px;
    font-size: 100px;
    line-height: 1;
    font-weight: bold;
    color: $smoke-color2;
    opacity: 0.3;
  }
  &_text {
    transition: 0.4s ease-in-out;
    margin-bottom: 22px;
  }
  .th-btn {
    background-color: $smoke-color2;
    color: $title-color;
    padding: 12.5px 20px;
    box-shadow: none;
    &:before,
    &:after {
      background-color: $smoke-color2;
    }
  }
  &:hover {
    width: 100%;
    &:before,
    &:after {
      opacity: 1;
      height: 120%;
      width: 120%;
      border-radius: 0;
    }
    .shape-icon {
      &:before {
        background-color: $white-color;
      }
    }
    .bg-shape {
      bottom: 0;
      opacity: 1;
    }
    .box-title {
      color: $white-color;
    }
    .th-btn {
      background-color: $white-color;
    }
    .service-card {
      &_text {
        color: $white-color;
      }
    }
  }
}
.service-sec {
  background-size: auto;
  background-position: top center;
  background-color: $smoke-color;
}

@include vxs {
  .service-card {
    padding: 40px 30px;
  }
}

/* Service Box ---------------------------------- */
.service-box {
  position: relative;
  box-shadow: 0px 10px 15px rgba(8, 14, 28, 0.06);
  background-color: $white-color;
  border-radius: 10px;
  text-align: center;
  overflow: hidden;
  transition: 0.4s ease-in-out;
  .bg-shape {
    position: absolute;
    bottom: -200px;
    left: 0;
    width: 100%;
    opacity: 0;
    transition: 0.4s ease-in-out;
    pointer-events: none;
    img {
      width: 100%;
    }
  }
  &_img {
    border-radius: 10px 10px 0 0;
    overflow: hidden;
    img {
      width: 100%;
      transition: 0.4s ease-in-out;
    }
  }
  .box-title {
    margin-bottom: 12px;
    a:hover {
      color: $smoke-color2;
    }
  }
  &_text {
    transition: 0.4s ease-in-out;
    max-width: 230px;
    margin: 0 auto 10px auto;
  }
  &_icon {
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
    background-color: $white-color;
    margin: -40px auto 0 auto;
    transition: 0.4s ease-in-out;
    border-radius: 50%;
    img {
      transition: 0.4s ease-in-out;
    }
  }
  &_content {
    position: relative;
    padding: 0 10px 25px 10px;
  }
  &:hover {
    background-color: $theme-color;
    .bg-shape {
      bottom: 0;
      opacity: 1;
    }
    .box-title {
      color: $white-color;
    }
    .link-btn {
      color: $white-color;
      &:before {
        background-color: $white-color;
        width: 100%;
      }
      &:hover {
        &:before {
          width: 70px;
        }
      }
    }
    .service-box {
      &_img {
        img {
          transform: scale(1.1);
        }
      }
      &_icon {
        background-color: $theme-color;
        img {
          transform: rotateY(180deg);
          filter: brightness(0) invert(1);
        }
      }
      &_text {
        color: $white-color;
      }
    }
  }
}

/* Service Grid ---------------------------------- */
.service-grid {
  position: relative;
  margin-top: -40px;
  .bg-shape {
    position: absolute;
    bottom: -200px;
    left: 0;
    width: 100%;
    opacity: 0;
    transition: 0.4s ease-in-out;
    pointer-events: none;
    img {
      width: 100%;
    }
  }
  &_content {
    box-shadow: 0px 10px 15px rgba(8, 14, 28, 0.06);
    background-color: $white-color;
    border-radius: 10px;
    text-align: center;
    padding: 70px 15px 40px 15px;
    transition: 0.4s ease-in-out;
    overflow: hidden;
  }
  &_icon {
    height: 80px;
    width: 80px;
    line-height: 80px;
    background-color: $theme-color;
    border-radius: 50%;
    text-align: center;
    position: relative;
    z-index: 2;
    margin: 0 auto 0 auto;
    transform: translateY(40px);
    img {
      position: relative;
      z-index: 2;
      filter: brightness(0) invert(1);
      transition: 0.4s ease-in-out;
    }
    &:after,
    &:before {
      content: "";
      position: absolute;
      inset: 0;
      background-color: $theme-color;
      @extend .ripple-animation;
      z-index: -2;
      border-radius: 50%;
      transition: all ease 0.4s;
    }
    &:after {
      animation-delay: 2s;
    }
  }
  .box-title {
    margin-bottom: 15px;
    a:hover {
      color: $smoke-color2;
    }
  }
  &_text {
    transition: 0.4s ease-in-out;
    max-width: 230px;
    margin: 0 auto 22px auto;
  }
  .th-btn {
    padding: 15.5px 18px;
  }
  &:hover {
    .bg-shape {
      bottom: 0;
      opacity: 1;
    }
    .box-title {
      color: $white-color;
    }
    .th-btn {
      background-color: $white-color;
      color: $theme-color;
      &:hover {
        color: $white-color;
      }
    }
    .service-grid {
      &_content {
        background-color: $theme-color;
      }
      &_icon {
        background-color: $white-color;
        img {
          transform: rotateY(180deg);
          filter: none;
        }
        &:before,
        &:after {
          background-color: $white-color;
        }
      }
      &_text {
        color: $white-color;
      }
    }
  }
}

/* Service 3D ---------------------------------- */
.service-3d {
  text-align: center;
  background: $white-color;
  border-radius: 10px;
  padding: 40px 12px;
  &_text {
    max-width: 315px;
    margin: 0 auto 22px auto;
  }
  &_icon {
    margin-bottom: 30px;
    img {
      transition: 0.4s ease-in-out;
    }
  }
  .th-btn {
    padding: 15.5px 18px;
    background-color: $smoke-color2;
    color: $title-color;
    box-shadow: none;
    &:before,
    &:after {
      background-color: $smoke-color2;
    }
    &:hover {
      color: $white-color;
      &:before,
      &:after {
        background-color: $theme-color;
      }
    }
  }
  &:hover {
    .service-3d {
      &_icon {
        img {
          transform: rotateY(180deg);
        }
      }
    }
  }
}

/* Service Card 6---------------------------------- */
.service-area6 {
  background: linear-gradient(
    180deg,
    rgba(237, 240, 244, 0) 0%,
    rgba(237, 240, 244, 0.85) 100%
  );
  overflow: hidden;
}
.service-item {
  border-radius: 30px;
  border: 1px solid $white-color;
  padding: 40px 30px;
  background: linear-gradient(180deg, #fff 77.78%, rgba(255, 255, 255, 0) 100%);
  transition: all 0.4s ease-in-out;

  &_icon {
    width: 80px;
    height: 80px;
    line-height: 80px;
    text-align: center;
    display: block;
    margin-bottom: 28px;
    background-color: #f3f5fc;
    border-radius: 50%;
  }

  &_text {
    font-size: 16px;
    margin-bottom: 15px;
  }

  .box-title {
    letter-spacing: -0.48px;
    margin-bottom: 10px;
    @include xl {
      font-size: 20px;
    }
  }

  img {
    transition: all 0.4s ease-in-out;
  }

  &:hover {
    .service-item {
      &_icon {
        img {
          transform: rotateY(360deg);
        }
      }
    }
  }
}

/* Service Card 7---------------------------------- */
.service-area7 {
  position: relative;
  background-color: #1987541c;
  border-radius: 30px;
  overflow: hidden;

  @include xxl {
    border-radius: 0;
  }
}
.service-grid7 {
  position: relative;
  background: $white-color;
  padding: 40px;
  border-radius: 20px;
  border: 1px solid #e3e7f0;
  transition: 0.4s ease-in-out;

  @include xl {
    padding: 25px;
  }

  &_icon {
    width: 100px;
    height: 100px;
    line-height: 100px;
    text-align: center;
    border: 1px solid #e3e7f0;
    background-color: $white-color;
    box-shadow: drop-shadow(0px 26px 65px rgba(232, 234, 238, 0.55));
    border-radius: 50%;
    z-index: 9;
    transition: 0.4s ease-in-out;

    img {
      transition: 0.4s ease-in-out;
    }
  }
  &_text {
    margin-bottom: 15px;
  }

  .box-title {
    font-weight: 600;
    margin: 25px 0 5px 0;
  }

  .icon-btn {
    border-radius: 99px;
    background-color: $white-color;
    border: 1px solid $border-color;
    color: $title-color;
    position: relative;
    z-index: 3;
    display: block;
    text-align: center;
    margin: auto;
    transition: all 0.4s ease-in-out;

    &:hover {
      background-color: $theme-color;
      border-color: $theme-color;
      color: $white-color;
    }
  }
  .line-btn {
    font-family: $body-font;
  }

  &:hover {
    .service-grid7 {
      &_img {
        img {
          transform: scale(1.1);
        }
      }

      &_icon {
        border: 1px solid $theme-color;

        &:before {
          transform: scaleX(1);
        }

        img {
          transform: rotateY(180deg);
        }
      }
    }
  }
}
/* Service Box 7---------------------------------- */
.service-box7 {
  text-align: center;
  &_thumb {
    border-radius: 20px;
    background: #1987541c;
    display: flex;
    align-items: end;
    margin-bottom: 50px;
    img {
      margin: 20px 20px 0 20px;
      border-radius: 10px 10px 0 0;
      width: -webkit-fill-available;
    }
  }
  &_content {
    margin: 0 30px;
    @include md {
      margin: 0;
    }
  }
  &_text {
    margin-bottom: -0.4em;
  }
}

/* Service Details ---------------------------------- */
.page-title {
  margin-top: -0.22em;
  font-size: 40px;
  margin-bottom: 20px;
}
.page-img {
  margin-bottom: 40px;
  border-radius: 5px;
  overflow: hidden;
}
.page-single {
  margin-bottom: 30px;
}

.service-feature {
  &-wrap {
    display: grid;
    grid-template-columns: auto auto;
    gap: 25px;
  }
  background-color: $white-color;
  padding: 30px 25px 30px 30px;
  border: 1px solid $border-color;
  box-shadow: 0px 10px 30px rgba(8, 14, 28, 0.06);
  border-radius: 5px;
  display: flex;
  align-items: center;
  gap: 15px;
  &_icon {
    background: $theme-color;
    box-shadow: 0px 6px 20px rgba(0, 96, 255, 0.6);
    border-radius: 5px;
    @include equal-size-lineHeight(90px);
    text-align: center;
  }
  &_title {
    font-size: 20px;
    margin-bottom: 12px;
  }
  &_text {
    font-size: 14px;
    line-height: 24px;
    margin-bottom: 0;
  }
}

@include xl {
  .page-title {
    font-size: 38px;
  }
}

@include xl {
  .service-feature {
    flex-direction: column;
    text-align: center;
    gap: 20px;
    padding: 30px 10px;
    &_text {
      margin-bottom: -0.5em;
      max-width: 260px;
      margin-left: auto;
      margin-right: auto;
    }
  }
}

@include lg {
  .page-title {
    font-size: 32px;
  }
}

@include sm {
  .page-title {
    font-size: 28px;
  }
}

@include xs {
  .page-title {
    font-size: 24px;
  }
  .service-feature-wrap {
    grid-template-columns: auto;
  }
}
