p.has-drop-cap {
  margin-bottom: 20px;
}

.page--item p:last-child .alignright {
  clear: right;
}

.blog-title,
.pagi-title,
.breadcumb-title {
  word-break: break-word;
}

.blocks-gallery-caption,
.wp-block-embed figcaption,
.wp-block-image figcaption {
  color: $body-color;
}

.bypostauthor,
.gallery-caption {
  display: block;
}

.page-links,
.clearfix {
  clear: both;
}

.page--item {
  margin-bottom: 30px;

  p {
    line-height: 1.8;
  }
}

.content-none-search {
  margin-top: 30px;
}

.wp-block-button.aligncenter {
  text-align: center;
}

.alignleft {
  display: inline;
  float: left;
  margin-bottom: 10px;
  margin-right: 1.5em;
}

.alignright {
  display: inline;
  float: right;
  margin-bottom: 10px;
  margin-left: 1.5em;
  margin-right: 1em;
}

.aligncenter {
  clear: both;
  display: block;
  margin-left: auto;
  margin-right: auto;
  max-width: 100%;
}

.gallery {
  margin-bottom: 1.5em;
  width: 100%
}

.gallery-item {
  display: inline-block;
  text-align: center;
  vertical-align: top;
  width: 100%;
  padding: 0 5px;
}

.wp-block-columns {
  margin-bottom: 1em;
}

figure.gallery-item {
  margin-bottom: 10px;
  display: inline-block
}

figure.wp-block-gallery {
  margin-bottom: 14px;
}

.gallery-columns-2 .gallery-item {
  max-width: 50%
}

.gallery-columns-3 .gallery-item {
  max-width: 33.33%
}

.gallery-columns-4 .gallery-item {
  max-width: 25%
}

.gallery-columns-5 .gallery-item {
  max-width: 20%
}

.gallery-columns-6 .gallery-item {
  max-width: 16.66%
}

.gallery-columns-7 .gallery-item {
  max-width: 14.28%
}

.gallery-columns-8 .gallery-item {
  max-width: 12.5%
}

.gallery-columns-9 .gallery-item {
  max-width: 11.11%
}

.gallery-caption {
  display: block;
  font-size: 12px;
  color: var(--body-color);
  line-height: 1.5;
  padding: .5em 0
}

.wp-block-cover p:not(.has-text-color),
.wp-block-cover-image-text,
.wp-block-cover-text {
  color: $white-color;
}

.wp-block-cover {
  margin-bottom: 15px;
}

.wp-caption-text {
  text-align: center;
}

.wp-caption {
  margin-bottom: 1.5em;
  max-width: 100%;

  .wp-caption-text {
    margin: .5em 0;
    font-size: 14px;
  }
}

.wp-block-media-text,
.wp-block-media-text.alignwide,
figure.wp-block-gallery {
  margin-bottom: 30px;
}

.wp-block-media-text.alignwide {
  background-color: $smoke-color;
}

.editor-styles-wrapper .has-large-font-size,
.has-large-font-size {
  line-height: 1.4;
}

.wp-block-latest-comments a {
  color: inherit;
}

.wp-block-button {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  .wp-block-button__link {
    color: #fff;

    &:hover {
      color: #fff;
      background-color: $theme-color;
    }
  }

  &.is-style-outline {
    .wp-block-button__link {
      background-color: transparent;
      border-color: $title-color;
      color: $title-color;

      &:hover {
        color: #fff;
        background-color: $theme-color;
        border-color: $theme-color;
      }
    }
  }

  &.is-style-squared {
    .wp-block-button__link {
      border-radius: 0;
    }
  }
}

ol.wp-block-latest-comments li {
  margin: 15px 0;
}

ul.wp-block-latest-posts {
  padding: 0;
  margin: 0;
  margin-bottom: 15px;

  a {
    color: inherit;

    &:hover {
      color: $theme-color;
    }
  }

  li {
    margin: 15px 0;
  }
}

.wp-block-search {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30px;

  .wp-block-search__input {
    width: 100%;
    max-width: 100%;
    border: 1px solid rgba($color: #000000, $alpha: 0.1);
    padding-left: 20px;
  }

  .wp-block-search__button {
    margin: 0;
    min-width: 110px;
    border: none;
    color: #fff;
    background-color: $theme-color;
    &.has-icon {
      min-width: 55px;
    }

    &:hover {
      background-color: $title-color;
      opacity: 0.8;
    }
  }
}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper {
  padding: 0;
  border: none;
  .wp-block-search__input {
    padding: 0 8px 0 25px;
  }
}


ul.wp-block-rss a {
  color: inherit;
}

.wp-block-group.has-background {
  padding: 15px 15px 1px;
  margin-bottom: 30px;
}

.wp-block-table td,
.wp-block-table th {
  border-color: rgba(0, 0, 0, 0.10);
}

.wp-block-table.is-style-stripes {
  border: 1px solid rgba(0, 0, 0, 0.10);
  margin-bottom: 30px;
}

.wp-block-table.is-style-stripes {
  border: 0;
  margin-bottom: 30px;
  border-bottom: 0;
  th, td {
    border-color: $border-color;
  }
}

.logged-in {

  // .header-layout1:not(.default-header),
  .will-sticky .sticky-active.active,
  .preloader .th-btn {
    top: 32px;

    @media (max-width: 782px) {
      top: 46px;
    }

    @media (max-width: 600px) {
      top: 0;
    }
  }
}

.post-password-form {
  margin-bottom: 30px;
  margin-top: 20px;

  p {
    display: flex;
    position: relative;
    gap: 15px;
    @include xs {
      flex-wrap: wrap;
    }
  }

  label {
    display: flex;
    align-items: center;
    flex: auto;
    margin-bottom: 0;
    line-height: 1;
    margin-top: 0;
    gap: 15px;
    @include xs {
      flex-wrap: wrap;
    }
  }

  input {
    width: 100%;
    border: none;
    height: 55px;
    padding-left: 25px;
    color: $body-color;
    border: 1px solid $border-color;
  }

  input[type="submit"] {
    padding-left: 0;
    padding-right: 0;
    margin: 0;
    width: 140px;
    border: none;
    color: #fff;
    background-color: $theme-color;
    text-align: center;

    &:hover {
      background-color: $title-color;
    }
  }
}

.page-links {
  clear: both;
  margin: 0 0 1.5em;
  padding-top: 1em;

  >.page-links-title {
    margin-right: 10px;
  }

  >span:not(.page-links-title):not(.screen-reader-text),
  >a {
    display: inline-block;
    padding: 5px 13px;
    background-color: $white-color;
    color: $title-color;
    border: 1px solid rgba($color: #000000, $alpha: 0.08);
    margin-right: 10px;

    &:hover {
      opacity: 0.8;
      color: $white-color;
      background-color: $theme-color;
      border-color: transparent;
    }

    &.current {
      background-color: $theme-color;
      color: $white-color;
      border-color: transparent;
    }
  }

  span.screen-reader-text {
    display: none;
  }
}


.blog-single {

  .wp-block-archives-dropdown {
    margin-bottom: 30px;
  }

  &.format-quote,
  &.format-link,
  &.tag-sticky-2,
  &.sticky {
    border-color: transparent;
    position: relative;


    .blog-content {
      background-color: $smoke-color;
      padding: 0;
      border: none;

      &:before {
        display: none;
      }
    }

    &:before { 
      content: '\f0c1';
      position: absolute;
      font-family: 'Font Awesome 6 Pro';
      font-size: 5rem;
      opacity: 0.3;
      right: 15px;
      line-height: 1;
      top: 15px;
      color: $theme-color;
      z-index: 1;
    }
  }

  &.tag-sticky-2,
  &.sticky {
    &::before {
      content: "Featured";
      right: 0;
      top: 0;
      font-size: 18px;
      color: $white-color;
      background-color: $theme-color;
      font-family: $title-font;
      opacity: 1;
      text-transform: capitalize;
      padding: 10px 23px;
      font-weight: 400;
    }
  }

  &.format-quote {
    &:before {
      content: "\f10e";
      top: 0;
    }
  }

  .blog-content {
    .wp-block-categories-dropdown.wp-block-categories,
    .wp-block-archives-dropdown {
      display: block;
      margin-bottom: 30px;
    }
  }
}

.blog-details {
  .blog-single {
    &:before {
      display: none;
    }

    .blog-content {
      background-color: transparent;
      overflow: hidden;
    }

    &.format-chat {
      .blog-meta {
        margin-bottom: 20px;
      }

      .blog-content>p:nth-child(2n) {
        background: $smoke-color;
        padding: 5px 20px;
      }
    }

    &.tag-sticky-2,
    &.sticky,
    &.format-quote,
    &.format-link {
      box-shadow: none;
      border: none;
      background-color: transparent;

      &:before {
        display: none;
      }
    }
  }
}

.th-search {
  background-color: #f3f3f3;
  margin-bottom: 30px;
  border: 1px solid #f3f3f3;

  .search-grid-content {
    padding: 30px;

    @include sm {
      padding: 20px;
    }
  }

  .search-grid-title {
    font-size: 20px;
    margin-bottom: 5px;
    margin-top: 0;

    a {
      color: inherit;

      &:hover {
        color: $theme-color;
      }
    }
  }

  .search-grid-meta {
    >* {
      display: inline-block;
      margin-right: 15px;
      font-size: 14px;

      &:last-child {
        margin-right: 0;
      }
    }

    a,
    span {
      color: $body-color;
    }
  }
}


@include lg {
  .blog-single {

    &.format-quote,
    &.format-link,
    &.tag-sticky-2,
    &.sticky {
      &:before {
        font-size: 14px;
        padding: 8px 16px;
      }
    }
    &.format-quote {
      &:before {
        top: 15px;
      }
    }
  }
 
}


@include sm {
  .blog-single {

    &.format-quote,
    &.format-link,
    &.tag-sticky-2,
    &.sticky {
      &:before {
        font-size: 14px;
        padding: 8px 16px;
      }
    }
  }
}


@media (max-width: 768px) {
  .wp-block-latest-comments {
    padding-left: 10px;
  }

  .page--content.clearfix+.th-comment-form {
    margin-top: 24px;
  }
}