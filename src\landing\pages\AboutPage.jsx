import React from 'react';
import LandingLayout from '../LandingLayout';
import Header from '../components/Header';
import Footer from '../components/Footer';

const AboutPage = () => {
  return (
    <LandingLayout>
      <Header />
      
      <h1 style={{ display: 'none' }}>Về Pay2S - Giải pháp thanh toán tự động</h1>
      
      {/* Breadcrumb */}
      <div className="breadcumb-wrapper" data-bg-src="/fe-assets/img/bg/breadcumb-bg.jpg">
        <div className="container">
          <div className="breadcumb-content">
            <h1 className="breadcumb-title">Về Pay2S</h1>
            <ul className="breadcumb-menu">
              <li><a href="/">Trang chủ</a></li>
              <li>Về Pay2S</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* About Content */}
      <div className="space">
        <div className="container">
          <div className="row">
            <div className="col-12 text-center">
              <h2>Về Pay2S</h2>
              <p>Nội dung về công ty sẽ được thêm vào đây...</p>
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </LandingLayout>
  );
};

export default AboutPage;
