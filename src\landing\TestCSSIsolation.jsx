import React from 'react';

const TestCSSIsolation = () => {
  return (
    <div style={{ padding: '20px', border: '2px solid red', margin: '20px' }}>
      <h2>Test CSS Isolation</h2>
      <p>Component này để test xem CSS của landing có ảnh hưởng đến app chính không.</p>
      
      <div className="container">
        <div className="row">
          <div className="col-12">
            <h3>Bootstrap Test</h3>
            <p>Nếu CSS isolation hoạt động đúng, Bootstrap classes ở đây sẽ không bị ảnh hưởng bởi landing CSS.</p>
          </div>
        </div>
      </div>
      
      <button className="btn btn-primary">Bootstrap Button</button>
      <button className="th-btn">Landing Button (sẽ không có style nếu isolation hoạt động)</button>
      
      <div className="hero-style7">
        <p>Landing specific class - sẽ không có style nếu isolation hoạt động</p>
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <h4>Test Navigation</h4>
        <a href="/client/dashboard" style={{ marginRight: '10px' }}>Go to Dashboard</a>
        <a href="/landing">Go to Landing</a>
      </div>
    </div>
  );
};

export default TestCSSIsolation;
