<?php include 'header.php'; ?>
<h1 style="display:none;">Gi<PERSON>i pháp thông báo thông báo chia sẻ biến động số dư</h1>
<!--==============================
Hero Area
==============================-->
<div class="th-hero-wrapper hero-1" id="hero">
    <div class="hero-img tilt-active">
        <img src="fe-assets/img/hero/hero_img_1_1.png" alt="Hero Image" />
    </div>
    <div class="container">
        <div class="hero-style1">
            <span class="hero-subtitle">Webhook linh hoạt</span>
            <h1 class="hero-title">ĐỐI SOÁT TÀI CHÍNH</h1>
            <h1 class="hero-title">
                <span class="text-theme fw-medium"> LINH HOẠT</span>
            </h1>
            <p class="hero-text">
                Bạn đang mất hàng giờ theo dõi số dư, đối chiếu thanh toán thủ công?
                Nhân viên không biết khách đã chuyển tiền hay chưa?<br>
                Doanh nghiệp của bạn cần tự động hóa quy trình thu ngân, trích lương, báo cáo tài chính?
            </p>
            <div class="btn-group d-flex flex-nowrap gap-2">
                <a href="#anchor-title" class="th-btn">Tìm hiểu thêm <i class="fa-regular fa-arrow-right ms-2"></i></a>
                <a href="https://pay2s.vn/client/" class="th-btn">Tài liệu tích hợp <i class="fa-regular fa-arrow-right ms-2"></i></a>
            </div>
        </div>
    </div>
    <div class="hero-shape1">
        <img src="fe-assets/img/hero/hero_shape_1_1.svg" alt="shape" />
    </div>
    <div class="hero-shape2">
        <img src="fe-assets/img/hero/hero_shape_1_2.svg" alt="shape" />
    </div>
    <div class="hero-shape3">
        <img src="fe-assets/img/hero/hero_shape_1_3.svg" alt="shape" />
    </div>
</div>
<!--======== / Hero Section ========-->

<!--==============================
Feature Area  
==============================-->
<div class="container space" style="color: black">
    <h2 class="sec-title text-center">So sánh giữa các nền tảng</h2>
    <table
        border="1"
        cellspacing="0"
        cellpadding="10"
        class="comparison-table">
        <thead>
            <tr>
                <th></th>
                <th>Thanh toán truyền thống</th>
                <th>Cổng thanh toán khác</th>
                <th>Cổng thanh toán Pay2s <span class="tag-hot">Hot</span></th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td data-label="Tính năng">Thanh toán QR Code</td>
                <td data-label="Truyền thống">✅</td>
                <td data-label="Khác">✅</td>
                <td data-label="Pay2s">✅</td>
            </tr>
            <tr>
                <td data-label="Tính năng">Xác nhận thanh toán ngay</td>
                <td data-label="Truyền thống">✅</td>
                <td data-label="Khác">✅</td>
                <td data-label="Pay2s">✅</td>
            </tr>
            <tr>
                <td data-label="Tính năng">Công nghệ</td>
                <td data-label="Truyền thống">Trung gian thanh toán VietQR</td>
                <td data-label="Khác">API ngân hàng VietQR</td>
                <td data-label="Pay2s">API ngân hàng VietQR</td>
            </tr>
            <tr>
                <td data-label="Tính năng">Mức phí mỗi giao dịch</td>
                <td data-label="Truyền thống">1.000đ + 1 đến 3% (*)</td>
                <td data-label="Khác">~350đ (**)</td>
                <td data-label="Pay2s">✅ Không tính phí trên giao dịch</td>
            </tr>
            <tr>
                <td data-label="Tính năng">Dòng tiền</td>
                <td data-label="Truyền thống">Giữ tại trung gian thanh toán</td>
                <td data-label="Khác">Về ngay tài khoản ngân hàng</td>
                <td data-label="Pay2s">✅Về ngay tài khoản ngân hàng</td>
            </tr>
            <tr>
                <td data-label="Tính năng">Đối soát</td>
                <td data-label="Truyền thống">Khó khăn</td>
                <td data-label="Khác">Dễ</td>
                <td data-label="Pay2s">✅ Rất dễ thông qua dashboard quản lý</td>
            </tr>
            <tr>
                <td data-label="Tính năng">Thủ tục đăng ký</td>
                <td data-label="Truyền thống">
                    Yêu cầu giấy phép đăng ký kinh doanh
                </td>
                <td data-label="Khác">Không yêu cầu, đăng ký sử dụng ngay</td>
                <td data-label="Pay2s">✅Không yêu cầu, đăng ký sử dụng ngay</td>
            </tr>
            <tr>
                <td data-label="Tính năng">Hỗ trợ cá nhân</td>
                <td data-label="Truyền thống">❌</td>
                <td data-label="Khác">✅</td>
                <td data-label="Pay2s">✅</td>
            </tr>
            <tr>
                <td data-label="Tính năng">Tích hợp nhanh chóng</td>
                <td data-label="Truyền thống">❌</td>
                <td data-label="Khác">✅</td>
                <td data-label="Pay2s">✅</td>
            </tr>
        </tbody>
    </table>
</div>

<!--==============================
Team Area  
==============================-->
<section class="" id="team-sec">
    <div class="container space">
        <div class="title-area text-center">
            <div class="shadow-title" id="anchor-title">API ngân hàng</div>
            <span class="sub-title">
                <div class="icon-masking me-2">
                    <span
                        class="mask-icon"
                        data-mask-src="fe-assets/img/theme-img/title_shape_2.svg"></span>
                    <img src="fe-assets/img/theme-img/title_shape_2.svg" alt="shape" />
                </div>
                Pay2S tự hào đồng hành
            </span>
            <h2 class="sec-title">
                Hợp tác cùng <span class="text-theme">Nhiều Ngân Hàng</span>
            </h2>
        </div>
    </div>
    <div
        class="brand-sec1"
        data-pos-for="#process-sec"
        data-sec-pos="top-half">
        <div class="container py-5">
            <div class="slider-area text-center">
                <div
                    class="swiper th-slider"
                    id="brandSlider1"
                    data-slider-options='{"breakpoints":{"0":{"slidesPerView":2},"576":{"slidesPerView":"2"},"768":{"slidesPerView":"3"},"992":{"slidesPerView":"3"},"1200":{"slidesPerView":"4"},"1400":{"slidesPerView":"5"}}}'>
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            <div class="brand-box py-20">
                                <img src="fe-assets/img/bank/MB.svg" alt="Brand Logo" />
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="brand-box py-20">
                                <img src="fe-assets/img/bank/ACB.svg" alt="Brand Logo" />
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="brand-box py-20">
                                <img
                                    src="fe-assets/img/bank/Techcombank.svg"
                                    alt="Brand Logo" />
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="brand-box py-20">
                                <img
                                    src="fe-assets/img/bank/Vietcombank.svg"
                                    alt="Brand Logo" />
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="brand-box py-20">
                                <img
                                    src="fe-assets/img/bank/vietinbank.svg"
                                    alt="Brand Logo" />
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="brand-box py-20">
                                <img src="fe-assets/img/bank/BIDV.svg" alt="Brand Logo" />
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="brand-box py-20">
                                <img src="fe-assets/img/bank/TP.svg" alt="Brand Logo" />
                            </div>
                        </div>
                        <div class="swiper-slide">
                            <div class="brand-box py-20">
                                <img src="fe-assets/img/bank/Momo.svg" alt="Brand Logo" />
                            </div>
                        </div>
                    </div>
                </div>
                <button
                    data-slider-prev="#brandSlider1"
                    class="slider-arrow style3 slider-prev">
                    <i class="far fa-arrow-left"></i>
                </button>
                <button
                    data-slider-next="#brandSlider1"
                    class="slider-arrow style3 slider-next">
                    <i class="far fa-arrow-right"></i>
                </button>
            </div>
        </div>
    </div>
    <div class="container text-center my-5">
        <h2 class="fw-bold mb-5">CHỨNG NHẬN HỢP TÁC VỚI NGÂN HÀNG</h2>

        <div class="row justify-content-center">
            <div class="col-md-5 mb-4">
                <div class="card border-0">
                    <img src="fe-assets/img/certificate/Cong-van-BIDV-Fute.jpg" class="img-fluid border border-4 border-warning rounded" alt="Giấy chứng nhận BIDV">
                    <div class="mt-2 text-muted small">
                        Công văn hợp tác giữa Ngân hàng TMCP Đầu tư và Phát triển Việt Nam và Công ty CP FUTE
                    </div>
                </div>
            </div>
            <div class="col-md-5 mb-4">
                <div class="card border-0">
                    <img src="fe-assets/img/certificate/Cong-van-MB-Fute.jpg" class="img-fluid border border-4 border-warning rounded" alt="Giấy chứng nhận MB Bank">
                    <div class="mt-2 text-muted small">
                        Công văn hợp tác giữa Ngân hàng TMCP Quân đội và Công ty CP FUTE
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="shape-mockup" data-top="0" data-right="0">
        <img src="fe-assets/img/shape/tech_shape_1.png" alt="shape" />
    </div>
    <div class="shape-mockup" data-top="0%" data-left="0%">
        <img src="fe-assets/img/shape/square_1.png" alt="shape" />
    </div>
</section>
<!--==============================
Cta Area  
==============================-->
<section class="space-bottom">
    <div class="container">
        <div class="cta-box">
            <div class="row">
                <div class="col-lg-5">
                    <div class="cta-box_img">
                        <img src="fe-assets/img/normal/customer_support.jpg" alt="Image" />
                    </div>
                </div>
                <div class="col-lg-7">
                    <div class="cta-box_content">
                        <div class="cta-box_icon">
                            <img src="fe-assets/img/icon/call_1.svg" alt="Icon" />
                        </div>
                        <div class="title-area mb-35">
                            <div class="shadow-title">TRIỂN KHAI</div>
                            <span class="sub-title">
                                <div class="icon-masking me-2">
                                    <span
                                        class="mask-icon"
                                        data-mask-src="fe-assets/img/theme-img/title_shape_2.svg"></span>
                                    <img
                                        src="fe-assets/img/theme-img/title_shape_2.svg"
                                        alt="shape" />
                                </div>
                                Hành động ngay
                            </span>
                            <h2 class="sec-title">
                                Triển khai giải pháp
                                <span class="text-theme">Cho doanh nghiệp</span> của bạn
                                ngay
                            </h2>
                        </div>
                        <a href="https://pay2s.vn/client/" class="th-btn">Xem bảng giá <i class="fa-solid fa-right-long"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!--==============================
Faq Area
==============================-->
<div class="overflow-hidden space" id="faq-sec">
    <div class="container">
        <div class="row align-items-center justify-content-center">
            <div class="col-xl-6 col-lg-9">
                <div class="title-area text-center text-xl-start">
                    <span class="sub-title">PAY2S FAQ'S</span>
                    <h2 class="sec-title">Bạn có thể thắc mắc</h2>
                </div>
                <div class="accordion-area accordion" id="faqAccordion">
                    <div class="accordion-card style3">
                        <div class="accordion-header" id="collapse-item-1">
                            <button
                                class="accordion-button collapsed"
                                type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#collapse-1"
                                aria-expanded="false"
                                aria-controls="collapse-1">
                                Pay2S sẽ giúp bạn những gì?
                            </button>
                        </div>
                        <div
                            id="collapse-1"
                            class="accordion-collapse collapse"
                            aria-labelledby="collapse-item-1"
                            data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p><strong>1. Thanh Toán QR Code Tự Động</strong></p>
                                <p> Tạo QR thanh toán ngay trong app của bạn, không phụ thuộc vào ví điện tử.

                                    Tỷ lệ thành công 99.9%, xử lý dưới 3 giây.</p>

                                <p><strong>2. Webhooks Đối Soát Real-time</strong></p>
                                <p>Nhận thông báo tự động khi khách hàng chuyển tiền.

                                    Đồng bộ trạng thái thanh toán vào hệ thống nội bộ.</p>

                                <p><strong>3. Trích Lương Tự Động</strong></p>
                                <p>Gửi lệnh chi lương hàng loạt qua API.

                                    Hỗ trợ webhook xác nhận thành công từ ngân hàng.</p>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-card style3 active">
                        <div class="accordion-header" id="collapse-item-2">
                            <button
                                class="accordion-button"
                                type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#collapse-2"
                                aria-expanded="true"
                                aria-controls="collapse-2">
                                API Pay2S có an toàn không?
                            </button>
                        </div>
                        <div
                            id="collapse-2"
                            class="accordion-collapse collapse show"
                            aria-labelledby="collapse-item-2"
                            data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p>Giải pháp Open Banking dành riêng cho doanh nghiệp Việt</p>

                                <p>Pay2S là đối tác chính thức được cấp API trực tiếp các ngân hàng lớn</p>
                                <p>✅ Kết nối trực tiếp 7 ngân hàng lớn (ACB, Techcombank, Vietcombank, MB, TPBank, BIDV, ViettinBank)</p>
                                <p>✅ Hỗ trợ đa nền tảng: Web, iOS, Android, POS</p>
                                <p>✅ Tài liệu rõ ràng + SDK hỗ trợ 10+ ngôn ngữ lập trình</p>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-card style3">
                        <div class="accordion-header" id="collapse-item-3">
                            <button
                                class="accordion-button collapsed"
                                type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#collapse-3"
                                aria-expanded="false"
                                aria-controls="collapse-3">
                                API ngân hàng và ví điện tử Pay2S có giới hạn giao dịch không?
                            </button>
                        </div>
                        <div
                            id="collapse-3"
                            class="accordion-collapse collapse"
                            aria-labelledby="collapse-item-3"
                            data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                <p class="faq-text">
                                    API ngân hàng và ví điện tử Pay2S không giới hạn số lần giao dịch và cũng không giới hạn cổng thanh toán, khách hàng chỉ cần đăng ký gói phù hợp với số lượng tài khoản cần thêm vào là có thể sử dụng.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-6 mt-35 mt-xl-0">
                <div class="img-box8">
                    <div class="img1">
                        <img src="fe-assets/img/normal/api-faq.svg" alt="Faq" />
                    </div>
                    <div class="shape2">
                        <img src="fe-assets/img/normal/p-vicon.png" alt="About" />
                    </div>
                    <div class="color-animate"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php include 'footer.php'; ?>