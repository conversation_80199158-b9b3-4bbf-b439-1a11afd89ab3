import React from 'react';
import LandingLayout from '../LandingLayout';
import Header from '../components/Header';
import Footer from '../components/Footer';

const ContactPage = () => {
  return (
    <LandingLayout>
      <Header />
      
      <h1 style={{ display: 'none' }}>Liên hệ Pay2S</h1>
      
      {/* Breadcrumb */}
      <div className="breadcumb-wrapper" data-bg-src="/fe-assets/img/bg/breadcumb-bg.jpg">
        <div className="container">
          <div className="breadcumb-content">
            <h1 className="breadcumb-title"><PERSON><PERSON><PERSON> hệ</h1>
            <ul className="breadcumb-menu">
              <li><a href="/">Trang chủ</a></li>
              <li><PERSON><PERSON>n hệ</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Contact Content */}
      <div className="space">
        <div className="container">
          <div className="row">
            <div className="col-lg-6">
              <h2>Thông tin liên hệ</h2>
              <div className="contact-info">
                <div className="contact-info-item">
                  <i className="fa-solid fa-house"></i>
                  <span>15/40/30 Đường số 59, Phường 14, Q.Gò Vấp, TP. HCM</span>
                </div>
                <div className="contact-info-item">
                  <i className="fa-solid fa-phone"></i>
                  <span>028 627 05478</span>
                </div>
                <div className="contact-info-item">
                  <i className="fa-solid fa-envelope"></i>
                  <span><EMAIL></span>
                </div>
              </div>
            </div>
            <div className="col-lg-6">
              <h2>Gửi tin nhắn</h2>
              <form className="contact-form">
                <div className="row">
                  <div className="col-md-6">
                    <input type="text" placeholder="Họ tên" className="form-control" />
                  </div>
                  <div className="col-md-6">
                    <input type="email" placeholder="Email" className="form-control" />
                  </div>
                  <div className="col-12">
                    <input type="text" placeholder="Tiêu đề" className="form-control" />
                  </div>
                  <div className="col-12">
                    <textarea placeholder="Nội dung" className="form-control" rows="5"></textarea>
                  </div>
                  <div className="col-12">
                    <button type="submit" className="th-btn">Gửi tin nhắn</button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </LandingLayout>
  );
};

export default ContactPage;
