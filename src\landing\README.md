# Landing Page với CSS Isolation

## Tổng quan

Thư mục này chứa landing page được chuyển đổi từ PHP sang React với CSS isolation để không ảnh hưởng đến app chính.

## Cấu trúc thư mục

```
src/landing/
├── LandingLayout.jsx          # Layout chính cho landing page
├── LandingPage.jsx            # Component chính landing page
├── LandingCSSLoader.jsx       # Component load và process CSS từ fe-assets (không dùng)
├── landing.css                # CSS cơ bản cho landing container
├── TestCSSIsolation.jsx       # Component test CSS isolation
├── components/
│   ├── Header.jsx             # Header component (dùng chung)
│   ├── Footer.jsx             # Footer component (dùng chung)
│   ├── HeroSection.jsx        # Hero section
│   └── BrandSection.jsx       # Brand slider section
├── pages/
│   ├── AboutPage.jsx          # Trang về chúng tôi
│   ├── PricingPage.jsx        # Trang bảng giá
│   └── ContactPage.jsx        # Trang liên hệ
└── README.md                  # File này
```

## Cách hoạt động CSS Isolation

### 1. Container Wrapper

- Tất cả landing page được wrap trong `.landing-container`
- CSS từ fe-assets được tự động prefix với `.landing-container`

### 2. Dynamic CSS Loading

- `LandingCSSLoader` component tự động:
  - Fetch CSS files từ `/src/fe-assets/css/`
  - Thêm prefix `.landing-container` cho tất cả CSS rules
  - Inject processed CSS vào `<head>`
  - Cleanup khi component unmount

### 3. Script Loading

- JavaScript từ fe-assets được load dynamic
- Tự động cleanup khi rời khỏi landing page

## Routes

- `/` - Landing page chính (home)
- `/landing` - Landing page
- `/about` - Trang về chúng tôi
- `/bang-gia` - Trang bảng giá
- `/lien-he` - Trang liên hệ
- `/test-css` - Test page để kiểm tra CSS isolation

## Cách sử dụng

### 1. Thêm section mới

```jsx
// Tạo component mới trong components/
const NewSection = () => {
  return <div className="new-section">{/* Nội dung section */}</div>;
};

// Thêm vào LandingPage.jsx
import NewSection from "./components/NewSection";

// Trong render
<LandingLayout>
  <LandingHeader />
  <HeroSection />
  <NewSection /> {/* Thêm ở đây */}
  <LandingFooter />
</LandingLayout>;
```

### 2. Thêm CSS tùy chỉnh

```css
/* Trong landing.css */
.landing-container .your-custom-class {
  /* CSS của bạn */
}
```

### 3. Thêm JavaScript tùy chỉnh

```jsx
// Trong component của bạn
useEffect(() => {
  // JavaScript code

  return () => {
    // Cleanup
  };
}, []);
```

## Test CSS Isolation

1. Truy cập `/test-css` để xem component test
2. Kiểm tra:
   - Bootstrap classes hoạt động bình thường
   - Landing-specific classes không có style
   - Navigation giữa landing và app hoạt động

## Lưu ý quan trọng

1. **Asset Paths**: Tất cả paths đến assets phải bắt đầu với `/fe-assets/` (đã copy vào public/)
2. **CSS Cleanup**: CSS sẽ tự động được remove khi rời khỏi landing page
3. **Script Conflicts**: Một số scripts có thể conflict với React, cần test kỹ
4. **Component Names**: Header và Footer được đặt tên chung để dùng cho nhiều trang

## Troubleshooting

### CSS không load

- Kiểm tra paths trong `LandingCSSLoader.jsx`
- Kiểm tra console để xem lỗi fetch

### JavaScript errors

- Kiểm tra thứ tự load scripts trong `LandingLayout.jsx`
- Một số jQuery plugins có thể cần init manual

### Styles bị conflict

- Kiểm tra CSS prefix có hoạt động đúng không
- Sử dụng browser dev tools để debug CSS specificity
