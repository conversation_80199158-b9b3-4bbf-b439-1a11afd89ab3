.process-card {
    &-area {
        position: relative;
        .process-line {
            position: absolute;
            left: 0;
            bottom: 20px;
            width: 100%;
            text-align: center;
        }
        .position-top {
            top: 42px;
            bottom: unset;
        }
    }
    &-wrap {
        padding: 30px 0 0 30px;
        &:has(.pulse) {
            padding: 0 12px;
            &:nth-child(even) {
                padding-top: 60px;
            }
        }
    }
    
    position: relative;
    box-shadow: 0px 10px 15px rgba(8, 14, 28, 0.06);
    border-radius: 10px;
    background-color: $white-color;
    text-align: center;
    padding: 30px 20px;
    max-width: 230px;
    margin-left: auto;
    margin-right: auto;
    &:has(.pulse) {
        margin-top: 52px;
    }
    .box-title {
        font-weight: 600;
        margin-bottom: 14px;
    }
    &_number,
    .pulse {
        height: 60px;
        width: 60px;
        line-height: 60px;
        background-color: $theme-color;
        border-radius: 50%;
        text-align: center;
        position: absolute;
        top: -30px;
        left: -30px;
        z-index: 2;
        font-size: 36px;
        font-weight: 700;
        color: $white-color;
        &:after,
        &:before {
            content: "";
            position: absolute;
            inset: 0;
            background-color: $theme-color;
            @extend .ripple-animation;
            z-index: -1;
            border-radius: 50%;
            transition: all ease 0.4s;
        }
        &:after {
            animation-delay: 2s;
        }
    }
    .pulse {
        width: 16px;
        height: 16px;
        position: absolute;
        top: -52px;
        left: calc(50% - 8px);
        &:before,
        &:after {
            animation-name: ripple2;
        }
    }
    &_icon {
        margin-bottom: 24px;
    }
    &_text {
        margin-bottom: -0.53em;
        font-size: 14px;
    }
}

@include lg {
    .process-card {
        &-area {
            .process-line {
                display: none;
            }
        }
        &-wrap {
            &:has(.pulse) {
                &:nth-child(even) {
                    padding-top: 30px;
                }
            }
        }
        &:has(.pulse) {
            margin-top: 8px;
        }
        .pulse {
            top: -8px;
        }
    }
}

@include xs {
    .process-card {
        &-wrap {
            &:has(.pulse) {
                &:nth-child(even) {
                    padding-top: 0;
                }
            }
        }
    }
}

/*process area 3*********************/
.process-area-3 {
    .sec-text {
        max-width: 430px;
        @include lg {
            margin-left: auto;
            margin-right: auto;
        }
    }
}
.process-card-area3 {
    position: relative;
    .process-line {
        margin: -268px -25px 0;
        position: absolute;
        z-index: -1;
        @include ml {
            max-width: 1100px;
        }
        @include xl {
            max-width: 900px;
            margin: -208px -25px 0;
        }
        @include lg {
            display: none;
        }
    }
    .process-card-wrap {
        padding-top: 0;
        padding-left: 80px;
        position: relative;
        z-index: 2;
        @include xl {
            padding-left: 20px;
        }
        @include xs {
            padding-left: 15px;
            padding-right: 15px;
        }
        &:first-child {
            padding-top: 40px;
            @include ml {
                padding-top: 30px;
            }
            @include xl {
                padding-top: 0px;
            }
        }
        &:nth-child(2) {
            margin-top: -40px;
            @include lg {
                margin-top: 40px;
            }
        }
        &:nth-child(3) {
            margin-top: -280px;
            @include xl {
                margin-top: -240px;
            }
            @include lg {
                margin-top: 40px;
            }
        }
    }
}
.process-card.style3 {
    text-align: start;
    box-shadow: none;
    border-radius: 0;
    padding: 0;
    max-width: 326px;
    background: transparent;
    margin-bottom: -0.3em;
    .process-card_icon {
        height: 64px;
        width: 64px;
        line-height: 64px;
        text-align: center;
        border-radius: 20px;
        background: $white-color;
        margin-bottom: 30px;
        img {
            transition: 0.4s;
        }
    }
    .process-card_number {
        background: transparent;
        width: auto;
        height: auto;
        left: auto;
        right: 0;
        color: $title-color;
        opacity: 0.06;
        font-size: 230px;
        font-weight: 800;
        line-height: 1;
        top: -50px;
        &:after,
        &:before {
            display: none;
        }
    }
    .process-card_text {
        font-size: 16px;
        margin-bottom: 13px;
    }
    &:hover {
        .process-card_icon {
            img {
                transform: rotateY(180deg);
            }
        }
    }
}