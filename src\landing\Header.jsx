import React from "react";
import logoSrc from "../assets/landing/img/logo.png";

const Header = () => {
  return (
    <header className="th-header header-layout6">
      <div className="sticky-wrapper">
        <div className="menu-area">
          <div className="container th-container4">
            <div className="row align-items-center justify-content-between">
              <div className="col-auto">
                <div className="header-logo">
                  <a className="icon-masking" href="/">
                    <img src={logoSrc} alt="pay2s" className="pay2s-logo" />
                  </a>
                </div>
              </div>
              <div className="col-auto">
                <nav className="main-menu style2 d-none d-lg-inline-block">
                  <ul>
                    <li className="menu-item">
                      <a href="/">Trang chủ</a>
                    </li>
                    <li className="menu-item-has-children">
                      <a href=""><PERSON><PERSON><PERSON></a>
                      <ul className="sub-menu">
                        <li>
                          <a href="/open-api-banking">API Open Banking</a>
                        </li>
                        <li>
                          <a href="/cong-thanh-toan">Cổng thanh toán tự động</a>
                        </li>
                        <li>
                          <a href="/chia-se-bien-dong-so-du">
                            Chia sẻ biến động số dư
                          </a>
                        </li>
                      </ul>
                    </li>
                    <li className="menu-item">
                      <a href="https://docs.pay2s.vn/">Trung tâm trợ giúp</a>
                    </li>
                    <li>
                      <a href="/bang-gia">Bảng giá</a>
                    </li>
                    <li className="menu-item">
                      <a href="https://pay2s.vn/tin-tuc/">Tin tức</a>
                    </li>
                    <li>
                      <a href="/lien-he">Liên hệ</a>
                    </li>
                  </ul>
                </nav>
                <button
                  type="button"
                  className="th-menu-toggle d-block d-lg-none"
                >
                  <i className="far fa-bars"></i>
                </button>
              </div>
              <div className="col-auto d-xl-block d-none">
                <div className="header-button">
                  <a
                    href="https://pay2s.vn/client/"
                    className="th-btn style-radius"
                  >
                    Đăng nhập
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
