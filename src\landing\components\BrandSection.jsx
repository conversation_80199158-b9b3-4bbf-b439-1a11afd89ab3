import React from 'react';

const BrandSection = () => {
  const brands = [
    'brand_3_1.svg',
    'brand_3_2.svg', 
    'brand_3_3.svg',
    'brand_3_4.svg',
    'brand_3_5.svg',
    'brand_3_6.svg'
  ];

  return (
    <div className="brand-sec3 overflow-hidden space-bottom">
      <div className="container th-container4">
        <div className="slider-area text-center">
          <div
            className="swiper th-slider"
            id="brandSlider3"
            data-slider-options='{"breakpoints":{"0":{"slidesPerView":2},"576":{"slidesPerView":"2"},"768":{"slidesPerView":"3"},"992":{"slidesPerView":"3"},"1200":{"slidesPerView":"4"},"1400":{"slidesPerView":"5"}}}'
          >
            <div className="swiper-wrapper">
              {/* Render brands twice for infinite scroll effect */}
              {[...brands, ...brands, ...brands].map((brand, index) => (
                <div key={index} className="swiper-slide">
                  <a href="https://pay2s.vn/client/" className="brand-box">
                    <img src={`/src/fe-assets/img/brand/${brand}`} alt="Brand Logo" />
                  </a>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrandSection;
