// src/components/TestDropdown.jsx
import React, { useEffect } from "react";
import { Dropdown } from "bootstrap";

function TestDropdown() {
  // useEffect để khởi tạo dropdown ngay khi component này được tạo
  useEffect(() => {
    const dropdownElement = document.getElementById("testDropdownButton");
    if (dropdownElement) {
      const bsDropdown = new Dropdown(dropdownElement);

      // Hàm dọn dẹp để hủy dropdown khi component mất đi
      return () => {
        if (bsDropdown) {
          bsDropdown.dispose();
        }
      };
    }
  }, []); // Mảng rỗng đảm bảo useEffect chỉ chạy một lần

  return (
    <div
      style={{
        padding: "50px",
        backgroundColor: "#f0f0f0",
        border: "2px solid red",
      }}
    >
      <h1>Đây là khu vực kiểm tra</h1>
      <p>
        Nếu dropdown dưới đây hoạt động, thì lỗi nằm ở file MasterLayout. N<PERSON><PERSON>
        không, lỗi nằm ở phần cài đặt chung của dự án.
      </p>
      <hr />
      <div className="dropdown">
        <button
          className="btn btn-success dropdown-toggle"
          type="button"
          id="testDropdownButton"
          data-bs-toggle="dropdown"
          aria-expanded="false"
        >
          Bấm vào đây
        </button>
        <ul className="dropdown-menu">
          <li>
            <a className="dropdown-item" href="#">
              Lựa chọn 1
            </a>
          </li>
          <li>
            <a className="dropdown-item" href="#">
              Lựa chọn 2
            </a>
          </li>
          <li>
            <a className="dropdown-item" href="#">
              Lựa chọn 3
            </a>
          </li>
        </ul>
      </div>
    </div>
  );
}

export default TestDropdown;
