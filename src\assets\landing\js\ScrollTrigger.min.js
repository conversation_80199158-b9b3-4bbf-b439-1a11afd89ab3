/*!
 * ScrollTrigger 3.3.3
 * https://greensock.com 
 * 
 * @license Copyright 2020, GreenSock. All rights reserved.
 * Subject to the terms at https://greensock.com/standard-license or for Club GreenSock members, the agreement issued with that membership.
 * @author: <PERSON>, <EMAIL>
 */
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e=e||self).window=e.window||{})}(this,(function(e){"use strict";function t(e){return e}function r(){return"undefined"!=typeof window}function n(){return _||r()&&(_=window.gsap)&&_.registerPlugin&&_}function o(e){return!!~N.indexOf(e)}function i(e,t){var r=t.s;return function(t){return arguments.length?e[r]=t:e[r]}}function s(e,t){var r=t.s,n=t.d2;return(r="scroll"+n)&&o(e)?Math.max(L[r],I[r])-(z["inner"+n]||L["client"+n]||I["client"+n]):e[r]-e["offset"+n]}function l(e){return"string"==typeof e}function a(e){return"function"==typeof e}function f(e){return"number"==typeof e}function c(e){return"object"==typeof e}function u(e){return z.getComputedStyle(e)}function p(e,t){for(var r in t)r in e||(e[r]=t[r]);return e}function d(e,t){var r=t&&"matrix(1, 0, 0, 1, 0, 0)"!==u(e)[H]&&_.to(e,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),n=e.getBoundingClientRect();return r&&r.progress(0).kill(),n}function g(e,t){var r=t.d2;return e["offset"+r]||e["client"+r]||0}function h(e,t,r,n){return r.split(",").forEach((function(r){return e(t,r,n)}))}function v(e,t,r){return e.addEventListener(t,r,{passive:!0})}function m(e,t,r){return e.removeEventListener(t,r)}function y(e,t){if(l(e)){var r=e.indexOf("="),n=~r?(e.charAt(r-1)+1)*parseFloat(e.substr(r+1)):0;n&&(e.indexOf("%")>r&&(n*=t/100),e=e.substr(0,r-1)),e=n+(e in xe?xe[e]*t:~e.indexOf("%")?parseFloat(e)*t/100:parseFloat(e)||0)}return e}function x(e,t,r,n,i,s,l){var a=i.startColor,f=i.endColor,c=i.fontSize,u=i.indent,p=i.fontWeight,d=A.createElement("div"),g=o(r),h=-1!==e.indexOf("scroller"),v=g?I:r,m=-1!==e.indexOf("start"),y=m?a:f,x="border-color:"+y+";font-size:"+c+";color:"+y+";font-weight:"+p+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return x+="position:"+(h&&g?"fixed;":"absolute;"),!h&&g||(x+=(n===ve?re:ne)+":"+(s+parseFloat(u))+"px;"),l&&(x+="box-sizing:border-box;text-align:left;width:"+l.offsetWidth+"px;"),d._isStart=m,d.setAttribute("class","gsap-marker-"+e),d.style.cssText=x,d.innerText=t||0===t?e+"-"+t:e,v.insertBefore(d,v.children[0]),d._offset=d["offset"+n.op.d2],be(d,0,n,g,m),d}function b(){return B=B||R(Pe)}function w(){B||(B=R(Pe),$||Ce("scrollStart"),$=V())}function S(){return!X&&200<V()-$&&F.restart(!0)}function k(e){for(var t=Ce("refreshInit"),r=we.length,n=r;n--;)we[n].scroll.rec=we[n].scroll();for(n=0;n<r;n++)we[n]&&we[n].refresh(!0!==e);for(t.forEach((function(e){return e&&e.render&&e.render(-1)})),n=we.length;n--;)we[n].scroll.rec=0;Ce("refresh")}function T(e,t,r){if(Ee(r),e.parentNode===t){var n=t.parentNode;n&&(n.insertBefore(e,t),n.removeChild(t))}}function C(e,t,r){if(e.parentNode!==t){for(var n,o=Oe.length,i=t.style,s=e.style;o--;)i[n=Oe[o]]=r[n];i.position="absolute"===r.position?"absolute":"relative",s[ne]=s[re]="auto",i.overflow="visible",i.boxSizing="border-box",i[oe]=g(e,he)+ge,i[ie]=g(e,ve)+ge,i[ce]=s[ue]=s[te]=s[ee]="0",s[oe]=r[oe],s[ie]=r[ie],s[ce]=r[ce],e.parentNode.insertBefore(t,e),t.appendChild(e)}}function P(e){for(var t=Me.length,r=e.style,n=[],o=0;o<t;o++)n.push(Me[o],r[Me[o]]);return n.t=e,n}function O(e,t,r,n,o,i,s,c,p,g,h,v){if(a(e)&&(e=e(c)),l(e)&&"max"===e.substr(0,3)&&(e=v+("="===e.charAt(4)?y("0"+e.substr(3),r):0)),f(e))s&&be(s,r,n,h,!0);else{a(t)&&(t=t(c));var m,x,b,w=D(t)[0]||I,S=d(w)||{},k=e.split(" ");S&&(S.left||S.top)||"none"!==u(w).display||(b=w.style.display,w.style.display="block",S=d(w),b?w.style.display=b:w.style.removeProperty("display")),m=y(k[0],S[n.d]),x=y(k[1]||"0",r),e=S[n.p]-p[n.p]-g+m+o-x,s&&be(s,x,n,h,r-x<20||s._isStart&&20<x),r-=r-x}if(i){var T=e+r,C=i._isStart;v="scroll"+n.d2,be(i,T,n,h,C&&20<T||!C&&(h?Math.max(I[v],L[v]):i.parentNode[v])<=T+1),h&&(p=d(s),h&&(i.style[n.op.p]=p[n.op.p]-n.op.m-i._offset+ge))}return Math.round(e)}function M(e,t){var r,n=o(e)?t.sc:i(e,t),s="_scroll"+t.p2;return e[s]=n,function t(o,i,l,a,f){var c=t.tween,u=i.onComplete;return c&&c.kill(),r=n(),i[s]=o,(i.modifiers={})[s]=function(e){return n()!==r?(c.kill(),t.tween=0,e=n()):a&&(e=l+a*c.ratio+f*c.ratio*c.ratio),r=Math.round(e)},i.onComplete=function(){t.tween=0,u&&u.call(c)},c=t.tween=_.to(e,i)}}var _,E,z,A,L,I,N,F,R,B,D,W,q,j,X,Y,H,U=1,V=Date.now,Z=V(),$=0,G=1,J=Math.abs,K="scrollLeft",Q="scrollTop",ee="left",te="top",re="right",ne="bottom",oe="width",ie="height",se="Right",le="Left",ae="Top",fe="Bottom",ce="padding",ue="margin",pe="Width",de="Height",ge="px",he={s:K,p:ee,p2:le,os:re,os2:se,d:oe,d2:pe,a:"x",sc:function(e){return arguments.length?z.scrollTo(e,ve.sc()):z.pageXOffset||A[K]||L[K]||I[K]||0}},ve={s:Q,p:te,p2:ae,os:ne,os2:fe,d:ie,d2:de,a:"y",op:he,sc:function(e){return arguments.length?z.scrollTo(he.sc(),e):z.pageYOffset||A[Q]||L[Q]||I[Q]||0}},me={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},ye={toggleActions:"play",anticipatePin:0},xe={top:0,left:0,center:.5,bottom:1,right:1},be=function(e,t,r,n,o){var i={},s=r[o?"os2":"p2"],l=r[o?"p2":"os2"];e._isFlipped=o,i[r.a+"Percent"]=o?-100:0,i[r.a]=o?1:0,i["border"+s+pe]=1,i["border"+l+pe]=0,i[r.p]=t,_.set(e,i)},we=[],Se={},ke={},Te=[],Ce=function(e){return ke[e]&&ke[e].map((function(e){return e()}))||Te},Pe=function(){var e=we.length,t=0,r=V(),n=50<=r-Z;for(n&&($&&!Y&&200<r-$&&($=0,Ce("scrollEnd")),q=Z,Z=r);t<e;t++)we[t]&&we[t].update(0,n);B=0},Oe=[ee,te,ne,re,ue+fe,ue+se,ue+ae,ue+le,"display","flexShrink"],Me=Oe.concat([oe,ie,"boxSizing","max"+pe,"max"+de,"position",ue,ce,ce+ae,ce+se,ce+fe,ce+le]),_e=/([A-Z])/g,Ee=function(e){for(var t,r,n=e.t.style,o=e.length,i=0;i<o;i+=2)r=e[i+1],t=e[i],r?n[t]=r:n[t]&&n.removeProperty(t.replace(_e,"-$1").toLowerCase())},ze={left:0,top:0},Ae=/(?:webkit|moz|length)/i;he.op=ve;var Le=(Ie.prototype.init=function(e,r){if(this.progress=0,this.vars&&this.kill(1),G){var n,h,b,k,E,L,N,F,R,B,j,H,Z,K,Q,ee,te,re,ne,de,xe,be,ke,Te,Ce,Pe,Oe,Me,_e,Le,Ne,Fe,Re,Be,De,We,qe=(e=p(l(e)||f(e)||e.nodeType?{trigger:e}:e,ye)).horizontal?he:ve,je=e.onUpdate,Xe=e.toggleClass,Ye=e.id,He=e.onToggle,Ue=e.onRefresh,Ve=e.scrub,Ze=e.trigger,$e=e.pin,Ge=e.pinSpacing,Je=e.invalidateOnRefresh,Ke=e.anticipatePin,Qe=e.onScrubComplete,et=e.onSnapComplete,tt=e.once,rt=e.snap,nt=e.pinReparent,ot=!Ve&&0!==Ve,it=D(e.scroller||z)[0],st=_.core.getCache(it),lt=o(it),at=[e.onEnter,e.onLeave,e.onEnterBack,e.onLeaveBack],ft=ot&&(tt?"play":e.toggleActions).split(" "),ct="markers"in e?e.markers:ye.markers,ut=lt?0:parseFloat(u(it)["border"+qe.p2+pe])||0,pt=this,dt=function e(){return Ie.removeEventListener("scrollEnd",e)||pt.refresh()},gt=e.onRefreshInit&&function(){return e.onRefreshInit(pt)};Ke*=45,we.push(pt),pt.scroller=it,pt.scroll=lt?qe.sc:i(it,qe),E=pt.scroll(),pt.vars=e,r=r||e.animation,st.tweenScroll=st.tweenScroll||{top:M(it,ve),left:M(it,he)},pt.tweenTo=n=st.tweenScroll[qe.p],r&&(r.vars.lazy=!1,r._initted||!1!==r.vars.immediateRender&&r.render(-.01,!0,!0),pt.animation=r.pause(),r.scrollTrigger=pt,(Re=f(Ve)&&Ve)&&(Fe=_.to(r,{ease:"power3",duration:Re,onComplete:function(){return Qe&&Qe(pt)}})),_e=0,Ye=Ye||r.vars.id),rt&&(c(rt)||(rt={snapTo:rt}),b=a(rt.snapTo)?rt.snapTo:"labels"===rt.snapTo?function(e){return function(t){var r,n=[],o=e.labels,i=e.duration();for(r in o)n.push(o[r]/i);return _.utils.snap(n,t)}}(r):_.utils.snap(rt.snapTo),Be=c(Be=rt.duration||{min:.1,max:2})?W(Be.min,Be.max):W(Be,Be),De=_.delayedCall(rt.delay||Re/2||.1,(function(){if(!$||$===Ne&&!Y){var e=r&&!ot?r.totalProgress():pt.progress,t=(e-Le)/(V()-q)*1e3||0,o=J(t/2)*t/.185,i=e+o,s=W(0,1,b(i,pt)),l=s-e-o,a=pt.scroll(),f=Math.round(N+s*K),c=n.tween;if(a<=F&&N<=a){if(c&&!c._initted){if(c.data<=Math.abs(f-a))return;c.kill()}n(f,{duration:Be(J(.185*Math.max(J(i-e),J(s-e))/t/.05||0)),ease:rt.ease||"power3",data:Math.abs(f-a),onComplete:function(){_e=Le=r&&!ot?r.totalProgress():pt.progress,et&&et(pt)}},N+e*K,o*K,l*K)}}else De.restart(!0)})).pause()),Ye&&(Se[Ye]=pt),Ze=pt.trigger=D(Ze||$e)[0],$e=!0===$e?Ze:D($e)[0],l(Xe)&&(Xe={targets:Ze,className:Xe}),$e&&(!1===Ge||Ge===ue||(Ge="flex"!==u($e.parentNode).display&&ce),pt.pin=$e,!1!==e.force3D&&_.set($e,{force3D:!0}),(h=_.core.getCache($e)).spacer?Q=h.pinState:(h.spacer=re=A.createElement("div"),re.setAttribute("class","pin-spacer"+(Ye?" pin-spacer-"+Ye:"")),h.pinState=Q=P($e)),pt.spacer=re=h.spacer,Me=u($e),Te=Me[Ge+qe.os2],de=_.getProperty($e),xe=_.quickSetter($e,qe.a,ge),C($e,re,Me),te=P($e)),ct&&(Z=c(ct)?p(ct,me):me,j=x("scroller-start",Ye,it,qe,Z,0),H=x("scroller-end",Ye,it,qe,Z,0,j),ne=j["offset"+qe.op.d2],R=x("start",Ye,it,qe,Z,ne),B=x("end",Ye,it,qe,Z,ne),lt||(function(e){e.style.position="absolute"===u(e).position?"absolute":"relative"}(it),_.set([j,H],{force3D:!0}),Pe=_.quickSetter(j,qe.a,ge),Oe=_.quickSetter(H,qe.a,ge))),pt.revert=function(e){var t=!1!==e;t!==k&&(pt.update(t),$e&&t&&T($e,re,Q),k=t)},pt.refresh=function(t){if(!X&&We)if($e&&t&&$)v(Ie,"scrollEnd",dt);else{var n=Math.max(pt.scroll(),pt.scroll.rec||0),o=pt.progress;X=1,Fe&&Fe.kill(),Je&&r&&r.progress(0).invalidate().progress(pt.progress),k||pt.revert();var i,f,c,p,h,m,x,b=(lt?z["inner"+qe.d2]:it["client"+qe.d2])||0,w=lt?ze:d(it),S=s(it,qe),T=0,M=0,E=e.end,A=e.endTrigger||Ze,L=e.start||($e||!Ze?"0 0":"0 100%"),D=$e&&Math.max(0,we.indexOf(pt))||0;if(D)for(m=D;m--;)we[m].pin===$e&&we[m].revert();if(N=O(L,Ze,b,qe,pt.scroll(),R,j,pt,w,ut,lt,S)||($e?-.001:0),a(E)&&(E=E(pt)),l(E)&&!E.indexOf("+=")&&(~E.indexOf(" ")?E=(l(L)?L.split(" ")[0]:"")+E:(T=y(E.substr(2),b),E=l(L)?L:N+T,A=Ze)),F=Math.max(N,O(E||(A?"100% 0":S),A,b,qe,pt.scroll()+T,B,H,pt,w,ut,lt,S))||-.001,K=F-N||(N-=.01)&&.001,$e){for(m=D;m--;)(x=we[m]).pin===$e&&x.start-x._pinPush<N&&(M+=x.end-x.start);if(N+=M,F+=M,pt._pinPush=M,R&&M&&((i={})[qe.a]="+="+M,_.set([R,B],i)),i=u($e),p=qe===ve,c=pt.scroll(),be=parseFloat(de(qe.a))+M,C($e,re,i),te=P($e),f=d($e,!0),Ge&&(re.style[Ge+qe.os2]=K+M+ge,(Ce=Ge===ce?g($e,qe)+K+M:0)&&(re.style[qe.d]=Ce+ge),lt&&pt.scroll(n)),lt&&((h={top:f.top+(p?c-N:0)+ge,left:f.left+(p?0:c-N)+ge,boxSizing:"border-box",position:"fixed"})[oe]=h.maxWidth=Math.ceil(f.width)+ge,h[ie]=h.maxHeight=Math.ceil(f.height)+ge,h[ue]=h[ue+ae]=h[ue+se]=h[ue+fe]=h[ue+le]="0",h[ce]=i[ce],h[ce+ae]=i[ce+ae],h[ce+se]=i[ce+se],h[ce+fe]=i[ce+fe],h[ce+le]=i[ce+le],ee=function(e,t,r){for(var n,o=[],i=e.length,s=r?8:0;s<i;s+=2)n=e[s],o.push(n,n in t?t[n]:e[s+1]);return o.t=e.t,o}(Q,h,nt)),r?(r.progress(1,!0),ke=de(qe.a)-be+K+M,K!==ke&&ee.splice(ee.length-2,2),r.progress(0,!0)):ke=K,D)for(m=0;m<D;m++)we[m].pin===$e&&we[m].revert(!1)}else if(Ze&&pt.scroll())for(f=Ze.parentNode;f&&f!==I;)f._pinOffset&&(N-=f._pinOffset,F-=f._pinOffset),f=f.parentNode;pt.start=N,pt.end=F,pt.scroll()<n&&pt.scroll(n),pt.revert(!1),X=0,o!==pt.progress&&(Fe&&r.totalProgress(o,!0),pt.progress=o,pt.update()),$e&&Ge&&(re._pinOffset=Math.round(pt.progress*ke)),Ue&&Ue(pt)}},pt.getVelocity=function(){return(pt.scroll()-L)/(V()-q)*1e3||0},pt.update=function(e,t){var o,i,l,a,f,c=pt.scroll(),p=e?0:(c-N)/K,g=p<0?0:1<p?1:p||0,h=pt.progress;if(t&&(L=E,E=c,rt&&(Le=_e,_e=r&&!ot?r.totalProgress():g)),Ke&&!g&&$e&&!X&&N<c+(c-L)/(V()-q)*Ke&&(g=1e-4),g!==h&&We){if(a=(f=(o=pt.isActive=!!g&&g<1)!=(!!h&&h<1))||!!g!=!!h,pt.direction=h<g?1:-1,pt.progress=g,ot||(!Fe||X||U?r&&r.totalProgress(g,!!X):(Fe.vars.totalProgress=g,Fe.invalidate().restart())),$e)if(e&&Ge&&(re.style[Ge+qe.os2]=Te),lt){if(a){if(l=!e&&c+1>=s(it,qe),nt){if(!X&&(o||l)){var v=d($e,!0),m=c-N;$e.style.top=v.top+(qe===ve?m:0)+ge,$e.style.left=v.left+(qe===ve?0:m)+ge}!function(e,t){if(e.parentNode!==t){var r,n,o=e.style;if(t===I)for(r in e._stOrig=o.cssText,n=u(e))+r||Ae.test(r)||!n[r]||"string"!=typeof o[r]||"0"===r||(o[r]=n[r]);else o.cssText=e._stOrig;t.appendChild(e)}}($e,X||!o&&!l?re:I)}Ee(o||l?ee:te),ke!==K&&g<1&&o||xe(be+(1!==g||l?0:ke))}}else xe(be+ke*g);!rt||n.tween||X||U||(Ne=$,De.restart(!0)),Xe&&f&&(!tt||o)&&D(Xe.targets).forEach((function(e){return e.classList[o?"add":"remove"](Xe.className)})),!je||ot||e||je(pt),a&&!X?(i=g&&!h?0:1===g?1:1===h?2:3,1===g&&tt?pt.kill():ot&&(l=!f&&"none"!==ft[i+1]&&ft[i+1]||ft[i],r&&("complete"===l||"reset"===l||l in r)&&("complete"===l?r.pause().totalProgress(1):"reset"===l?r.restart(!0).pause():r[l]()),je&&je(pt)),!f&&U||(He&&f&&He(pt),at[i]&&at[i](pt),tt&&(at[i]=0),f||at[i=1===g?1:3]&&at[i](pt))):ot&&je&&!X&&je(pt)}Oe&&(Pe(c+(j._isFlipped?1:0)),Oe(c))},pt.enable=function(){We||(We=!0,v(it,"resize",S),v(it,"scroll",w),gt&&v(Ie,"refreshInit",gt),r&&(r.add?_.delayedCall(.01,pt.refresh)&&(K=.01)&&(N=F=0):pt.refresh()))},pt.disable=function(e){if(We&&(We=pt.isActive=!1,Fe&&Fe.pause(),e!==We&&pt.update(1),$e&&T($e,re,Q),gt&&m(Ie,"refreshInit",gt),De&&(De.pause(),n.tween&&n.tween.kill()),!lt)){for(var t=we.length;t--;)if(we[t].scroller===it&&we[t]!==pt)return;m(it,"resize",S),m(it,"scroll",w)}},pt.kill=function(e){pt.disable(e),Ye&&delete Se[Ye],we.splice(we.indexOf(pt),1),r&&(r.scrollTrigger=null)},pt.enable()}else this.update=this.refresh=this.kill=t},Ie.register=function(e){if(_=e||n(),r()&&window.document&&(z=window,A=document,L=A.documentElement,I=A.body),_&&(D=_.utils.toArray,W=_.utils.clamp,_.core.globals("ScrollTrigger",Ie),I)){R=z.requestAnimationFrame||function(e){return setTimeout(e,16)},v(z,"mousewheel",w),N=[z,A,L,I],v(A,"scroll",w);var o,i=I.style,s=i.borderTop;i.borderTop="1px solid #000",o=d(I),ve.m=Math.round(o.top+ve.sc())||0,he.m=Math.round(o.left+he.sc())||0,s?i.borderTop=s:i.removeProperty("border-top"),j=setInterval(b,100),_.delayedCall(.5,(function(){return U=0})),v(A,"touchcancel",t),v(I,"touchstart",t),h(v,A,"pointerdown,touchstart,mousedown",(function(){return Y=1})),h(v,A,"pointerup,touchend,mouseup",(function(){return Y=0})),H=_.utils.checkPrefix("transform"),Me.push(H),E=V(),F=_.delayedCall(.2,k).pause(),v(A,"visibilitychange",(function(){return A.hidden||k()})),v(A,"DOMContentLoaded",k),v(z,"load",(function(){return $||k()})),v(z,"resize",S)}return E},Ie.defaults=function(e){for(var t in e)ye[t]=e[t]},Ie.kill=function(){G=0,we.slice(0).forEach((function(e){return e.kill(1)}))},Ie);function Ie(e,t){E||Ie.register(_)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),this.init(e,t)}Le.version="3.3.3",Le.create=function(e,t){return new Le(e,t)},Le.refresh=function(e){return e?S():k(!0)},Le.update=Pe,Le.maxScroll=function(e,t){return s(e,t?he:ve)},Le.getScrollFunc=function(e,t){return(t=t?he:ve)&&(o(e)?t.sc:i(e,t))},Le.getById=function(e){return Se[e]},Le.getAll=function(){return we.slice(0)},Le.syncInterval=function(e){return clearInterval(j)||(j=e)&&setInterval(b,e)},Le.isScrolling=function(){return!!$},Le.addEventListener=function(e,t){var r=ke[e]||(ke[e]=[]);~r.indexOf(t)||r.push(t)},Le.removeEventListener=function(e,t){var r=ke[e],n=r&&r.indexOf(t);0<=n&&r.splice(n,1)},Le.batch=function(e,t){function r(e,t){var r=[],n=[],o=_.delayedCall(s,(function(){t(r,n),r=[],n=[]})).pause();return function(e){r.length||o.restart(!0),r.push(e.trigger),n.push(e),l<=r.length&&o.progress(1)}}var n,o=[],i={},s=t.interval||.016,l=t.batchMax||1e9;for(n in t)i[n]="on"===n.substr(0,2)&&a(t[n])&&"onRefreshInit"!==n?r(0,t[n]):t[n];return a(l)&&(l=l(),Le.addEventListener("refresh",(function(){return l=t.batchMax()}))),D(e).forEach((function(e){var t={};for(n in i)t[n]=i[n];t.trigger=e,o.push(Le.create(t))})),o},n()&&_.registerPlugin(Le),e.ScrollTrigger=Le,e.default=Le,"undefined"==typeof window||window!==e?Object.defineProperty(e,"__esModule",{value:!0}):delete e.default}));