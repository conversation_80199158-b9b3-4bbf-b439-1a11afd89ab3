/* About 1 ---------------------------------- */
.img-box1 {
  position: relative;
  z-index: 2;
  margin-right: 18px;
  margin-left: 50px;
  .img1 {
    img {
      width: 100%;
      max-width: 650px;
    }
  }
  .shape1 {
    position: absolute;
    bottom: 0px;
    right: 80px;
    z-index: -1;
    img {
      max-width: fit-content;
    }
  }
  .year-counter {
    position: absolute;
    bottom: 30px;
    right: 0;
  }
}
.year-counter {
  height: 184px;
  width: 184px;
  background-color: $theme-color;
  border: 12px solid $white-color;
  box-shadow: 0px 10px 30px rgba(8, 14, 28, 0.1);
  border-radius: 50%;
  text-align: center;
  padding: 25px 0;
  &_number {
    color: $white-color;
    font-size: 64px;
    margin-bottom: 0px;
    line-height: 1.1;
  }
  &_text {
    color: $white-color;
    margin-bottom: 0;
    font-weight: 400;
  }
}
.about-feature {
  &-wrap {
    display: grid;
    grid-template-columns: auto auto;
    gap: 20px;
    padding-bottom: 35px;
    border-bottom: 1px solid $border-color;
    margin-bottom: 40px;
  }
  display: flex;
  gap: 10px;
  align-items: center;
  &_title {
    font-size: 20px;
    margin-bottom: 7px;
  }
  &_text {
    margin-bottom: 0;
  }
}
.about-feature.style2 {
  gap: 30px;
  align-items: start;
  &:not(:last-child) {
    margin-bottom: 37px;
  }
  .about-feature_icon {
    height: 64px;
    width: 64px;
    line-height: 64px;
    display: inline-block;
    background: $theme-color;
    border-radius: 16px;
    text-align: center;
    color: $white-color;
    font-size: 28px;
    i {
      transition: 0.4s;
    }
  }
  .about-feature_title {
    font-size: 24px;
    font-weight: 600;
    margin-top: -0.2em;
    margin-bottom: 13px;
  }
  .about-feature_text {
    margin-bottom: -0.4em;
  }
  &:hover {
    .about-feature_icon {
      i {
        transform: rotateY(360deg);
      }
    }
  }
}
.about-feature.style3 {
  gap: 20px;
  max-width: 390px;
  .about-feature_icon {
    height: 70px;
    width: 70px;
    line-height: 70px;
    text-align: center;
    background: #1987541c;
    border-radius: 16px;
    align-self: start;
  }
  .about-feature_title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
  }
  &:not(:last-child) {
    margin-bottom: 40px;
  }
}
.call-btn {
  display: flex;
  align-items: center;
  gap: 20px;
  .play-btn {
    > i {
      --icon-size: 56px;
      background-color: $theme-color;
      color: $white-color;
    }
    &:before,
    &:after {
      background-color: $theme-color;
    }
    &:hover {
      > i {
        background-color: $title-color;
      }
      &:before,
      &:after {
        background-color: $title-color;
      }
    }
  }
  .btn-text {
    font-size: 14px;
    font-weight: 500;
    display: block;
  }
  .btn-title {
    font-size: 20px;
    color: $title-color;
    font-weight: 600;
  }
  a:hover {
    color: $theme-color;
  }
}

.btn-group {
  &:has(.call-btn) {
    gap: 50px;
  }
}

@include sm {
  .img-box1 {
    margin-right: 0;
    margin-left: 0;
    .year-counter {
      bottom: 0;
    }
  }
}

@include xs {
  .btn-group {
    &:has(.call-btn) {
      gap: 30px;
    }
  }
  .about-feature {
    &-wrap {
      grid-template-columns: auto;
      padding-bottom: 25px;
      margin-bottom: 35px;
    }
  }
}

/* About 2 ---------------------------------- */
@media (min-width: 1300px) {
  .img-box3 {
    margin-right: 56px;
  }
}
.img-box3 {
  position: relative;
  z-index: 2;
  line-height: 1px;
  .img1 {
    display: inline-block;
    border-radius: 10px;
    overflow: hidden;
    margin-left: 15px;
  }
  .img2 {
    position: absolute;
    bottom: 0;
    right: 0;
    background-color: $white-color;
    padding: 10px 0 0 10px;
    border-radius: 10px;
    animation: jumpAni 7s linear infinite;
    img {
      border-radius: inherit;
    }
  }
  .img3 {
    position: absolute;
    left: -90px;
    top: 80px;
    animation: jumpReverseAni 7s linear infinite;
    img {
      border-radius: 10px;
    }
  }
  .shape1 {
    position: absolute;
    top: 80px;
    left: -195px;
    z-index: -1;
    animation: movingX 8s linear infinite;
  }
}
@include xl {
  .img-box3 {
    .img3 {
      left: -40px;
    }
  }
}
@include lg {
  .img-box3 {
    margin-left: 150px;
  }
}
@include sm {
  .img-box3 {
    margin-left: 0;
  }
}
@include xs {
  .img-box3 {
    .img3 {
      left: -28px;
      max-width: 35%;
    }
    .img2 {
      max-width: 70%;
    }
    .shape1 {
      max-width: 50%;
      left: -30px;
      top: 40%;
    }
  }
}
/* About 3 ---------------------------------- */
.achivement-tab {
  display: flex;
  background-color: $title-color;
  position: relative;
  button {
    color: $white-color;
    border: none;
    padding: 0 5px;
    line-height: 56px;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    flex: 50%;
    position: relative;
    z-index: 2;
    background-color: transparent;
    &:after {
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border-style: solid;
      border-width: 10px 10px 0 10px;
      border-color: $theme-color transparent transparent transparent;
      z-index: 1;
      opacity: 0;
      transition: 0.4s;
    }
    &.active {
      background: $theme-color;
      &:after {
        opacity: 1;
      }
    }
  }
  .indicator {
    position: absolute;
    top: var(--pos-y);
    left: var(--pos-x);
    height: var(--height-set);
    width: var(--width-set);
    background-color: $theme-color;
    transition: 0.4s ease-in-out;
    &:after {
      content: "";
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border-style: solid;
      border-width: 10px 10px 0 10px;
      border-color: $theme-color transparent transparent transparent;
      z-index: 1;
    }
  }
}
.achivement-box {
  &-area {
    background-color: $white-color;
    box-shadow: 0px 4px 50px rgba(8, 14, 28, 0.1);
  }
  display: flex;
  gap: 30px;
  align-items: center;
  max-width: 100%;
  margin: 30px;
  &_img {
    border-radius: 5px;
    overflow: hidden;
    min-width: 218px;
    img {
      width: 100%;
    }
  }
  .box-title {
    margin-bottom: 12px;
  }
  &_text {
    font-size: 14px;
    margin-bottom: 14px;
  }
}

@media (min-width: 1300px) {
  .img-box4 {
    margin-left: -90px;
    margin-right: 56px;
  }
}

.img-box4 {
  position: relative;
  text-align: center;
  img {
    width: 100%;
    max-width: 750px;
  }
  .img-shape {
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1;
  }
  .play-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

@include sm {
  .achivement-box {
    flex-direction: column;
    &_img {
      min-width: 100%;
    }
  }
}

@include vxs {
  .achivement-tab button {
    font-size: 14px;
    text-transform: capitalize;
  }
}

/* About Feature ---------------------------------- */
.about-sec-v4 {
  padding-top: 67px;
}
.img-box6 {
  position: relative;
  .shape1 {
    position: absolute;
    top: 0;
    right: 0;
    animation: jumpReverseAni 7s linear infinite;
  }
  .shape2 {
    position: absolute;
    top: 0;
    left: -40px;
    animation: moving 8s linear infinite;
  }
}
.color-animate {
  width: 100%;
  max-width: 500px;
  aspect-ratio: 16 / 16;
  background-color: #f2ba4c;
  opacity: 0.45;
  filter: blur(300px);
  border-radius: 50%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  animation: bgColor 8s ease-in-out infinite;
}
@include md {
  .img-box6 {
    text-align: center;
    .shape2 {
      left: 0;
    }
  }
  .list-center {
    max-width: 480px;
    margin-left: auto;
    margin-right: auto;
  }
  .list-center.two-column {
    max-width: 500px;
  }
}

@include xs {
  .img-box6 {
    .shape1,
    .shape2 {
      max-width: 130px;
    }
  }
}

/* About 4 ---------------------------------- */
.about-sec4 {
  position: relative;
  min-height: 770px;
  border-radius: 30px;
  z-index: 2;
  background-attachment: fixed;

  @include md {
    height: 472px;
  }
  .about-shape4 {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 512px;
    border-radius: 30px;
    background-color: $theme-color;
    z-index: -1;
    overflow: hidden;
  }
}

.marquee-wrapper {
  position: relative;
  overflow: hidden;
  z-index: 9;
  transform: rotate(-45deg);
  margin-right: -75%;

  @include lg {
    margin-right: -90%;
  }

  @include md {
    margin-right: -100%;
  }

  @include sm {
    margin-right: -140%;
    margin-top: 20%;
  }

  @media (max-width: 530px) {
    margin-right: -190%;
    margin-top: 20%;
  }

  @media (max-width: 330px) {
    margin-right: -210%;
    margin-top: 50%;
  }

  .marquee {
    position: relative;
    --duration: 120s;
    --gap: 0px;
    display: -ms-flexbox;
    display: flex;
    overflow: hidden;
    user-select: none;
    gap: var(--gap);

    &:first-child {
      --duration: 100s;
    }

    &:last-child {
      --duration: 80s;
    }

    &.marquee--reverse {
      .marquee-group {
        animation-direction: reverse;
      }
    }

    .marquee-group {
      -ms-flex-negative: 0;
      flex-shrink: 0;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      -ms-flex-pack: distribute;
      justify-content: space-around;
      gap: var(--gap);
      min-width: 100%;
      animation: scroll var(--duration) linear infinite;

      .text {
        font-family: $title-font;
        font-size: 16px;
        font-weight: 600;
        letter-spacing: -0.32px;
        border-radius: 100px;
        color: $white-color;
        padding: 6px 30px;
        margin: 8px 5px;
        display: inline-block;
        cursor: pointer;
        border: 1px solid rgba(92, 127, 255, 0.62);
        background: rgba(255, 255, 255, 0.01);
        box-shadow: 0px 3px 15px 0px rgba(0, 0, 0, 0.2);
        transition: all 0.4s ease-in-out;

        &:hover {
          background-color: $white-color;
          color: $theme-color;
        }
      }
    }
  }
}

@media (prefers-reduced-motion: reduce) {
  .marquee-wrapper .marquee .marquee-group {
    -webkit-animation-play-state: play;
    animation-play-state: play;
  }
}

/* About 5 ---------------------------------- */
.about-area5 {
  border: 1px solid #e3e7f0;
  padding: 80px;
  border-radius: 30px;

  @include md {
    padding: 80px 40px;
  }

  @include sm {
    padding: 80px 20px;
  }
}
.th-experience {
  position: absolute;
  bottom: 80px;
  left: 0px;
  z-index: 2;
  padding: 30px 30px 30px 40px;
  background: $theme-color;
  border-radius: 25px 0px 0px 25px;
  width: 340px;
  color: $white-color;

  @include xl {
    left: -30px;
  }

  @include vxs {
    bottom: 0;
    padding: 20px;
  }

  &_content {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .experience-year {
    font-size: 54px;
    font-weight: 700;
    letter-spacing: -1.08px;
    color: $white-color;
    margin-bottom: 0px;
  }

  .experience-text {
    font-family: $title-font;
    font-size: 18px;
    font-weight: 600;
    line-height: 22px;
    color: $white-color;
    margin-bottom: 0;
  }

  &.style2 {
    position: relative;
    width: 120px;
    bottom: 0;
    background-color: $white-color;
    border-radius: 15px;
    padding: 25px 18px;
    border: 1px solid #f2f2f2;
    box-shadow: 0px 13px 25px 0px rgba(0, 0, 0, 0.05);

    @include xl {
      left: 0;
    }

    .th-experience {
      &_content {
        display: block;
        text-align: center;
      }
    }

    .experience-year {
      font-size: 36px;
      color: $theme-color;
      margin-bottom: 0px;
    }

    .experience-text {
      font-size: 16px;
      font-weight: 400;
      color: $title-color;
      margin-bottom: 0;
    }
  }
}
.img-box6 {
  position: relative;
  z-index: 2;
  text-align: right;

  .img1 {
    position: relative;
    text-align: right;
    z-index: 2;
    border-radius: 30px;

    @include xs {
      text-align: center;
    }

    img {
      border-radius: 30px;
    }

    @include lg {
      margin-top: 50px;
    }
  }

  .th-experience {
    text-align: left;
  }
}
.down-option-area {
  position: relative;
  border-radius: 30px;
  border: 1px solid #e3e7f0;
  background: #f8faff;
  padding: 0 30px;
  margin-top: 30px;

  @include md {
    padding: 30px;
  }

  &_wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;

    @include md {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0 30px;
    }

    @include sm {
      grid-template-columns: repeat(1, 1fr);
    }

    .about-counter {
      display: flex;
      gap: 17px;
      align-items: center;
      padding: 30px 0;
      margin-right: 50px;

      @include xl {
        margin-right: 0;
      }

      @include lg {
        display: block;
      }

      @include md {
        padding: 0;
      }

      @include sm {
        margin: 20px 0;
      }

      &:before {
        content: "";
        position: absolute;
        top: -2px;
        width: 1px;
        height: 124px;
        right: 25%;
        background: #e2e4ea;
        transform: matrix(-0.97, -0.26, -0.26, 0.97, 0, 0);

        @include lg {
          display: none;
        }
      }

      .cilent-box_title {
        text-transform: uppercase;
        font-size: 14px;
        font-weight: 700;
        line-height: 20px;
        display: block;
        color: $title-color;
      }
    }

    .about_review {
      display: block;
      margin-bottom: 3px;

      i {
        color: #ffbe11;
        font-size: 14px;
        margin-right: 3px;
      }
    }

    .cilent-box {
      &_counter {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 0;
      }

      .counter-number {
        font-size: 14px;
        font-weight: 700;
      }
    }
  }
}
.about-client-box {
  position: relative;
  display: flex;
  gap: 20px;
  align-items: center;
  padding: 30px 0;

  @include md {
    padding: 0;
  }

  @include vxs {
    display: block;
  }

  &:before {
    content: "";
    position: absolute;
    right: -35%;
    top: -2px;
    width: 1px;
    height: 124px;
    transform: rotate(-15deg);
    background: #e2e4ea;

    @include lg {
      display: none;
    }
  }
  .cilent-box_title {
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 700;
    line-height: 20px;
    color: $title-color;
  }
}
.client-thumb-group {
  display: flex;

  @include vxs {
    margin-bottom: 20px;
  }

  .thumb {
    flex: none;

    &:not(:first-child) {
      margin-left: -30px;
    }

    img {
      border-radius: 50%;
      height: 60px;
    }
  }
}

/* About 8 ---------------------------------- */
.img-box10 {
  .img1 {
    position: relative;
    display: inline-block;
    .img2 {
      position: absolute;
      right: 0;
      bottom: 100px;
      @include vxs {
        img {
          max-width: 200px;
        }
      }
    }
  }
}
.about-sec-8 {
  margin: 0 100px;
  @include xxl {
    margin: 0 60px;
    padding: 0 30px;
  }
  @include ml {
    margin: 0 30px;
  }
  @include lg {
    margin: 0 15px;
  }
  @include xs {
    padding: 0 15px;
  }
}

/* About 9 ---------------------------------- */
.img-box11 {
  text-align: end;
  position: relative;
  display: inline-block;
  padding: 0 80px 0 174px;
  @include lg {
    padding: 0 40px 0 70px;
  }
  .img2 {
    bottom: 100px;
    left: 0;
    box-shadow: 10px 10px 50px 10px rgba(0, 31, 96, 0.2);
    border-radius: 20px;
    display: inline-block;
    position: absolute;
  }
  &:after {
    content: "";
    position: absolute;
    inset: 299px 0 54px 75px;
    border-radius: 30px;
    background: $theme-color;
    z-index: -1;
    @include lg {
      inset: 299px 0 54px 25px;
    }
  }
}

.img-box12 {
  text-align: end;
  position: relative;
  display: inline-block;
  padding: 0 250px 0 80px;
  @include xl {
    padding: 0 170px 0 80px;
  }
  @include lg {
    padding: 0 70px 0 40px;
  }
  .img2 {
    bottom: 100px;
    right: 0;
    box-shadow: 10px 10px 50px 10px rgba(0, 31, 96, 0.2);
    border-radius: 20px;
    display: inline-block;
    position: absolute;
  }
  &:after {
    content: "";
    position: absolute;
    inset: 299px 75px 54px 0;
    border-radius: 30px;
    background: $theme-color;
    z-index: -1;
    @include lg {
      inset: 299px 20px 54px 0;
    }
  }
}
