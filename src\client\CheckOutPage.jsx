import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import { callOrderApi, callUserApi } from "../callapi/OrderApi";

const CheckOutPage = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const planData = location.state;

  // State quản lý toàn bộ component
  const [vatRequest, setVatRequest] = useState(false);
  const [promoCode, setPromoCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [discountPercentage, setDiscountPercentage] = useState(0); // Lưu % giảm giá, ví dụ: 20
  const [originalAmount] = useState(planData?.amount || 0);
  const [promoApplied, setPromoApplied] = useState(false);
  const [promoMessage, setPromoMessage] = useState("");
  const [showInvoiceExistsModal, setShowInvoiceExistsModal] = useState(false);

  // State cho thông tin xuất hóa đơn VAT
  const [vatInfo, setVatInfo] = useState({
    taxCode: "",
    companyName: "",
    address: "",
    email: "",
  });
  const [vatValidationMessage, setVatValidationMessage] = useState("");

  // Helper function để lấy user_id một cách an toàn
  const getUserId = () => {
    const userId = localStorage.getItem("user_id");
    if (
      !userId ||
      userId === "null" ||
      userId === "undefined" ||
      userId.trim() === ""
    ) {
      return null;
    }
    return userId.trim();
  };

  // Redirect về trang chọn gói nếu không có dữ liệu gói
  useEffect(() => {
    if (!planData) {
      navigate("/client/pricetable");
    }
  }, [planData, navigate]);

  // Tính toán các giá trị liên quan đến tiền tệ
  const discountAmount = originalAmount * (discountPercentage / 100);
  const finalAmount = originalAmount - discountAmount;

  // Xử lý khi người dùng yêu cầu/hủy yêu cầu xuất hóa đơn VAT
  const handleVatRequest = async (e) => {
    const isChecked = e.target.checked;
    setVatRequest(isChecked);
    setVatValidationMessage(""); // Xóa thông báo lỗi cũ

    if (isChecked) {
      await loadVatInfo();
    } else {
      setVatInfo({ taxCode: "", companyName: "", address: "", email: "" });
    }
  };

  // Tải thông tin VAT của người dùng từ API
  const loadVatInfo = async () => {
    const userId = getUserId();
    if (!userId) {
      setVatValidationMessage("Vui lòng đăng nhập để sử dụng chức năng này.");
      return;
    }

    try {
      const response = await callUserApi({
        action: "get_profile",
        user_id: userId,
      });
      if (response.data.status) {
        const userInfo = response.data.message || {};
        const vatData = {
          taxCode: userInfo.tax_number || "",
          companyName: userInfo.company_name || "",
          address: userInfo.address || "",
          email: userInfo.email || "",
        };
        setVatInfo(vatData);

        // Kiểm tra xem thông tin VAT đã đầy đủ chưa
        const requiredFields = ["taxCode", "companyName", "address", "email"];
        const isInfoMissing = requiredFields.some(
          (field) => !vatData[field]?.trim()
        );
        if (isInfoMissing) {
          setVatValidationMessage(
            "Thông tin VAT chưa đầy đủ. Vui lòng cập nhật để xuất hóa đơn."
          );
        }
      } else {
        setVatValidationMessage(
          `Không thể tải thông tin VAT: ${response.data.message}`
        );
      }
    } catch (error) {
      console.error("Lỗi khi tải thông tin VAT:", error);
      setVatValidationMessage("Có lỗi xảy ra khi tải thông tin VAT.");
    }
  };

  // Xử lý khi người dùng áp dụng mã khuyến mãi
  const handleApplyPromoCode = async () => {
    if (!promoCode.trim()) {
      setPromoMessage("Vui lòng nhập mã khuyến mãi.");
      return;
    }
    const userId = getUserId();
    if (!userId) {
      setPromoMessage("Vui lòng đăng nhập để sử dụng mã khuyến mãi.");
      return;
    }

    setIsLoading(true);
    setPromoMessage("");

    try {
      const response = await callOrderApi({
        action: "checkpromocode",
        user_id: userId,
        promo_code: promoCode,
      });

      if (response.data.status) {
        const discount = response.data.discount || 0;
        setDiscountPercentage(discount);
        setPromoApplied(true);
        setPromoMessage(
          `Áp dụng mã thành công! Bạn được giảm giá ${discount}%.`
        );
      } else {
        setPromoMessage(response.data.message || "Mã khuyến mãi không hợp lệ.");
        setDiscountPercentage(0);
        setPromoApplied(false);
      }
    } catch (error) {
      console.error("Lỗi khi áp dụng mã khuyến mãi:", error);
      setPromoMessage("Có lỗi xảy ra khi áp dụng mã khuyến mãi.");
      setDiscountPercentage(0);
      setPromoApplied(false);
    } finally {
      setIsLoading(false);
    }
  };

  // Xử lý khi người dùng nhấn nút thanh toán
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Kiểm tra thông tin VAT nếu được yêu cầu
    if (vatRequest) {
      const requiredFields = ["taxCode", "companyName", "address", "email"];
      const isInfoMissing = requiredFields.some(
        (field) => !vatInfo[field]?.trim()
      );
      if (isInfoMissing) {
        setVatValidationMessage(
          "Vui lòng cập nhật đầy đủ thông tin VAT để xuất hóa đơn."
        );
        return;
      }
    }

    setIsLoading(true);
    const userId = getUserId();
    if (!userId) {
      alert("Vui lòng đăng nhập để tiếp tục.");
      setIsLoading(false);
      return;
    }

    try {
      const createInvoiceData = {
        action: "create_plan_account_and_invoice",
        user_id: userId,
        plan_id: planData?.planId?.toString() || "1",
        billing_cycle: mapCycleToPeriod(planData?.billingCycle),
        amount: originalAmount.toString(), // Luôn gửi số tiền gốc
        vat: vatRequest ? "1" : "0",
      };

      // SỬA LỖI: Thêm 'coupon' (thay vì 'promo_code') vào request nếu có
      if (promoApplied && promoCode) {
        createInvoiceData.coupon = promoCode;
      }

      // Log dữ liệu gửi đi khi thanh toán
      console.log("Payload gửi khi thanh toán:", createInvoiceData);

      const response = await callOrderApi(createInvoiceData);

      if (response.data.status) {
        const invoiceDetails = response.data.invoiceDetails;
        if (invoiceDetails?.payUrl) {
          window.location.href = invoiceDetails.payUrl;
        } else {
          alert("Không nhận được URL thanh toán.");
        }
      } else {
        if (response.data.message?.includes("đã có gói")) {
          setShowInvoiceExistsModal(true);
        } else {
          alert(`Có lỗi xảy ra: ${response.data.message}`);
        }
      }
    } catch (error) {
      console.error("Lỗi khi tạo hóa đơn:", error);
      alert("Có lỗi xảy ra khi xử lý yêu cầu của bạn.");
    } finally {
      setIsLoading(false);
    }
  };

  // Chuyển đổi chu kỳ thanh toán sang định dạng API yêu cầu
  const mapCycleToPeriod = (cycle) => {
    const mapping = {
      "1m": "monthly",
      "3m": "quarterly",
      "6m": "semi-annually",
      "12m": "annually",
    };
    return mapping[cycle] || "monthly";
  };

  // Component Modal thông báo hóa đơn đã tồn tại
  const InvoiceExistsModal = ({ show }) => {
    useEffect(() => {
      if (show) {
        const timer = setTimeout(() => {
          navigate("/client/invoice");
        }, 3000);
        return () => clearTimeout(timer);
      }
    }, [show]);

    if (!show) return null;

    return (
      <div
        className="modal fade show d-block"
        style={{ backgroundColor: "rgba(0,0,0,0.5)" }}
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title text-danger">Không thể tạo gói mới</h5>
            </div>
            <div className="modal-body text-center">
              <p>Bạn đã có hóa đơn chưa thanh toán. Vui lòng kiểm tra lại.</p>
              <div className="alert alert-warning">
                Đang chuyển hướng đến trang hóa đơn...
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Không render gì nếu chưa có dữ liệu gói
  if (!planData) return null;

  return (
    <MasterLayout>
      <Breadcrumb title={planData?.isUpgrade ? "Nâng cấp gói" : "Thanh toán"} />
      <InvoiceExistsModal show={showInvoiceExistsModal} />

      <form onSubmit={handleSubmit} className="checkout-form">
        <div className="container px-3 px-md-4">
          <div className="row g-3 g-lg-4">
            {/* Cột trái: Thông tin thanh toán */}
            <div className="col-12 col-lg-7 col-xl-8">
              {/* Card Kỳ hạn thanh toán */}
              <div className="card shadow-sm mb-3">
                <div className="card-header fw-bold">1. Kỳ hạn thanh toán</div>
                <div className="card-body">
                  <div className="form-check p-3 rounded border bg-light">
                    <input
                      className="form-check-input"
                      type="radio"
                      checked
                      readOnly
                    />
                    <label className="form-check-label fw-medium">
                      {planData?.selectedOption || "Thanh toán 1 tháng"}
                    </label>
                  </div>
                </div>
              </div>

              {/* Card Hình thức thanh toán */}
              <div className="card shadow-sm mb-3">
                <div className="card-header fw-bold">
                  2. Hình thức thanh toán
                </div>
                <div className="card-body">
                  <div className="form-check p-3 rounded border bg-light">
                    <input
                      className="form-check-input"
                      type="radio"
                      checked
                      readOnly
                    />
                    <label className="form-check-label fw-medium">
                      Chuyển khoản ngân hàng
                    </label>
                  </div>
                </div>
              </div>

              {/* Card Xuất hóa đơn VAT */}
              <div className="card shadow-sm">
                <div className="card-header fw-bold">3. Xuất hóa đơn</div>
                <div className="card-body">
                  <div className="form-check form-switch p-3">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      role="switch"
                      checked={vatRequest}
                      onChange={handleVatRequest}
                    />
                    <label className="form-check-label fw-medium">
                      Yêu cầu xuất hóa đơn VAT
                    </label>
                  </div>

                  {vatRequest && (
                    <>
                      <hr />
                      {vatValidationMessage && (
                        <div className="alert alert-warning d-flex justify-content-between align-items-center">
                          <span>{vatValidationMessage}</span>
                          {vatValidationMessage.includes("cập nhật") && (
                            <a
                              href="/client/profile"
                              className="btn btn-sm btn-success"
                            >
                              Cập nhật
                            </a>
                          )}
                        </div>
                      )}
                      <div className="row g-3">
                        <div className="col-12 col-md-6">
                          <label className="form-label fw-medium">
                            Mã số thuế
                          </label>
                          <input
                            type="text"
                            readOnly
                            className="form-control"
                            value={vatInfo.taxCode}
                            placeholder="Chưa có thông tin"
                          />
                        </div>
                        <div className="col-12 col-md-6">
                          <label className="form-label fw-medium">
                            Tên doanh nghiệp
                          </label>
                          <input
                            type="text"
                            readOnly
                            className="form-control"
                            value={vatInfo.companyName}
                            placeholder="Chưa có thông tin"
                          />
                        </div>
                        <div className="col-12">
                          <label className="form-label fw-medium">
                            Địa chỉ
                          </label>
                          <input
                            type="text"
                            readOnly
                            className="form-control"
                            value={vatInfo.address}
                            placeholder="Chưa có thông tin"
                          />
                        </div>
                        <div className="col-12">
                          <label className="form-label fw-medium">
                            Email nhận hóa đơn
                          </label>
                          <input
                            type="email"
                            readOnly
                            className="form-control"
                            value={vatInfo.email}
                            placeholder="Chưa có thông tin"
                          />
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Cột phải: Tóm tắt hóa đơn */}
            <div className="col-12 col-lg-5 col-xl-4">
              <div
                className="card shadow-sm position-lg-sticky"
                style={{ top: "20px" }}
              >
                <div className="card-header fw-bold">Thông tin hóa đơn</div>
                <div className="card-body">
                  <div className="d-flex justify-content-between mb-3">
                    <div>
                      <h6 className="mb-1 fw-bold text-success">
                        Gói {planData?.planName || "Basic"}
                      </h6>
                      <small className="text-muted">
                        {planData?.selectedOption || "x 1 tháng"}
                      </small>
                    </div>
                    <div className="fw-bold text-success">
                      {originalAmount.toLocaleString()} <small>VNĐ</small>
                    </div>
                  </div>
                  <hr />
                  <div className="d-flex justify-content-between mb-2">
                    <span>Tạm tính</span>
                    <span className="fw-medium">
                      {originalAmount.toLocaleString()} VNĐ
                    </span>
                  </div>

                  {promoApplied && discountAmount > 0 && (
                    <div className="d-flex justify-content-between mb-3 text-success">
                      <span>Giảm giá ({discountPercentage}%)</span>
                      <span className="fw-medium">
                        - {discountAmount.toLocaleString()} VNĐ
                      </span>
                    </div>
                  )}

                  <div className="mb-3">
                    <div className="input-group">
                      <input
                        type="text"
                        className="form-control text-uppercase"
                        placeholder="Mã khuyến mãi"
                        value={promoCode}
                        onChange={(e) =>
                          setPromoCode(e.target.value.toUpperCase())
                        }
                        disabled={promoApplied || isLoading}
                      />
                      <button
                        className="btn btn-success"
                        type="button"
                        onClick={handleApplyPromoCode}
                        disabled={promoApplied || isLoading}
                      >
                        {isLoading && promoCode
                          ? "..."
                          : promoApplied
                          ? "ĐÃ DÙNG"
                          : "ÁP DỤNG"}
                      </button>
                      {promoApplied && (
                        <button
                          className="btn btn-outline-danger"
                          type="button"
                          onClick={() => {
                            setPromoApplied(false);
                            setPromoCode("");
                            setDiscountPercentage(0);
                            setPromoMessage("");
                          }}
                          disabled={isLoading}
                        >
                          XÓA MÃ
                        </button>
                      )}
                    </div>
                    {promoMessage && (
                      <small
                        className={`mt-1 d-block ${
                          promoApplied ? "text-success" : "text-danger"
                        }`}
                      >
                        {promoMessage}
                      </small>
                    )}
                  </div>
                  <hr />

                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <span className="fw-bold fs-5">Tổng cộng</span>
                    <span className="fw-bold fs-4 text-success">
                      {finalAmount.toLocaleString()} VNĐ
                    </span>
                  </div>

                  <div className="d-grid">
                    <button
                      type="submit"
                      className="btn btn-success btn-lg fw-bold py-3"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <span
                            className="spinner-border spinner-border-sm me-2"
                            role="status"
                          ></span>
                          ĐANG XỬ LÝ...
                        </>
                      ) : planData?.isUpgrade ? (
                        "NÂNG CẤP NGAY"
                      ) : (
                        "THANH TOÁN NGAY"
                      )}
                    </button>
                  </div>
                  <div className="text-center mt-3">
                    <small className="text-muted">
                      Thanh toán được bảo mật bởi SSL
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </MasterLayout>
  );
};

export default CheckOutPage;
