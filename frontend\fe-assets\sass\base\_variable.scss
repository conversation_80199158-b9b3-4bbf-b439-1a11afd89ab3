:root {
  --theme-color         : #684DF4;
  --title-color         : #141D38;
  --body-color          : #737887;
  --smoke-color         : #F5F5F5;
  --smoke-color2        : #E2E8FA;
  --black-color         : #000000;
  --black-color2        : #080E1C;
  --gray-color          : #bdbdbd;
  --white-color         : #ffffff;
  --light-color         : #72849B;
  --yellow-color        : #FFB539;
  --success-color       : #28a745;
  --error-color         : #dc3545;
  --th-border-color     : #E0E0E0;
  --title-font          : 'Barlow', sans-serif;
  --body-font           : 'Roboto', sans-serif;
  --icon-font           : "Font Awesome 6 Pro";
  --main-container      : 1220px;
  --container-gutters   : 24px;
  --section-space       : 120px;
  --section-space-mobile: 80px;
  --section-title-space : 60px;
  --ripple-ani-duration : 5s;
}

// Color Variation
$theme-color          : var(--theme-color);
$title-color          : var(--title-color);
$body-color           : var(--body-color);
$smoke-color          : var(--smoke-color);
$smoke-color2         : var(--smoke-color2);
$white-color          : var(--white-color);
$light-color          : var(--light-color);
$black-color          : var(--black-color);
$black-color2         : var(--black-color2);
$gray-color           : var(--gray-color);
$yellow-color         : var(--yellow-color);
$success-color        : var(--success-color);
$error-color          : var(--error-color);
$border-color         : var(--th-border-color);

// Font Variation
$icon-font   : var(--icon-font);

// Typography
$title-font      : var(--title-font);
$body-font       : var(--body-font);
$body-font-size  : 16px;
$body-line-Height: 26px;
$body-font-weight: 400;
$p-line-Height   : 1.75;

// Device Variation
$hd: 1921px; // Large Device Than 1920
$xxl: 1500px; // Extra large Device
$ml: 1399px; // Medium Large Device
$xl: 1299px; // Medium Large Device
$lg: 1199px; // Large Device (Laptop)
$md: 991px; // Medium Device (Tablet)
$sm: 767px; // Small Device
$xs: 575px; // Extra Small Device
$vxs: 375px; // Extra Small Device

// Spacing Count with 5x
$space-count: 10;

// Section Space  For large Device
$space         : var(--section-space);
$space-extra   : calc(var(--section-space) - 30px);
$space-extra2   : calc(var(--section-space) - 40px);

// Section Space On small Device
$space-mobile         : var(--section-space-mobile);
$space-mobile-extra: calc(var(--section-space-mobile) - 30px);


// BG Color Mapping 
$bgcolorMap  : ();
$bgcolorMap  : map-merge((
  "theme"    : $theme-color,
  "smoke"    : $smoke-color,
  "smoke2"   : $smoke-color2,
  "white"    : $white-color,
  "black"    : $black-color,
  "black2"   : $black-color2,
  "title"    : $title-color,
), $bgcolorMap);


// Overlay Color Mapping 
$overlaycolorMap : ();
$overlaycolorMap : map-merge((
  "theme"        : $theme-color,
  "title"        : $title-color,
  "white"        : $white-color,
  "black"        : $black-color,
  "overlay1"     : #080E1C,
), $overlaycolorMap);


// Text Color Mapping 
$textColorsMap : ();
$textColorsMap : map-merge((
  "theme"      : $theme-color,
  "title"      : $title-color,
  "body"       : $body-color,
  "white"      : $white-color,
  "light"      : $light-color,
  "yellow"     : $yellow-color,
  "success"    : $success-color,
  "error"      : $error-color), 
$textColorsMap);


// Font Mapping 
$fontsMap    : ();
$fontsMap    : map-merge((
  "icon"     : $icon-font,
  "title"    : $title-font,
  "body"     : $body-font,
), $fontsMap);