<?php include 'header.php'; ?>

<!--==============================
    Breadcumb
============================== -->
<div class="breadcumb-wrapper" data-bg-src="fe-assets/img/hero/hero_bg_7_1.png">
    <div class="container">
        <div class="breadcumb-content">
            <h1 class="breadcumb-title">T<PERSON><PERSON> thuận sử dụng phần mềm Pay2S</h1>
            <ul class="breadcumb-menu">
                <li><a href="/">Trang chủ</a></li>
                <li>T<PERSON><PERSON> thuận sử dụng phần mềm Pay2S</li>
            </ul>
        </div>
    </div>
</div>
<!--==============================
    Project Area
==============================-->
<section class="space-top space-extra-bottom">
    <div class="container">
        <div class="row">
            <div class="col-xxl-8 col-lg-8">
                <div class="page-single">
                    <div class="kt-inside-inner-col">

                        <p>Đây là thỏa thuận pháp lý giữa khách hàng với Công Ty Cổ Phần FUTE. Quy định các điều khoản trong việc khách hàng sử dụng dịch vụ phần mềm Pay2S. Thỏa thuận này là hợp đồng điện tử giữa hai bên.</p>



                        <p>Bằng cách nhấp chuột vào nút “Đồng ý” khi đăng ký sử dụng, khách hàng đồng ý rằng các điều khoản này sẽ được áp dụng nếu khách hàng lựa chọn truy cập hoặc sử dụng dịch vụ và thao tác nhấp chuột này tương đương với việc hai bên đã ký kết hợp đồng.</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">1.Các thuật ngữ sử dụng trong thỏa thuận</h2>



                        <p>1.1 Phần mềm: Phần mềm mang tên Pay2S do Công Ty Cổ Phần FUTE cung cấp.</p>



                        <p>1.2 Hệ thống: Bao gồm các máy chủ đặt tại trung tâm dữ liệu của Pay2S, được cài đặt các phần mềm hệ thống và phần mềm Pay2S.</p>



                        <p>1.3 Pay2S: Là Công Ty Cổ Phần FUTE, nhà cung cấp dịch vụ phần mềm Pay2S.</p>



                        <p>1.4 Khách hàng: Là tổ chức hoặc cá nhân đứng ra đăng ký dùng thử hoặc trả tiền sử dụng dịch vụ phần mềm Pay2S.</p>



                        <p>1.5 Phí khởi tạo: Là khoản phí mà khách hàng phải trả một lần duy nhất cho Pay2S để khởi tạo sử dụng phần mềm lần đầu tiên.</p>



                        <p>1.6 Phí thuê bao: Là khoản phí mà khách hàng phải trả cho Pay2S để duy trì sử dụng phần mềm. Phí thuê bao được tính hàng tháng và khách hàng có thể thanh toán trước cho nhiều tháng khi đặt mua.</p>



                        <p>1.7 Thời gian thuê bao: Là khoảng thời gian khách hàng được cấp quyền sử dụng dịch vụ phần mềm Pay2S theo yêu cầu đăng ký và thỏa thuận thanh toán phí với Pay2S .</p>



                        <p>1.8 Gia hạn thuê bao: Là việc Pay2S cấp thêm thời gian sử dụng dịch vụ phần mềm Pay2S cho khách hàng theo thỏa thuận của hai bên.</p>



                        <p>1.9 Thông tin phái sinh: là các thông tin được tổng hợp, suy luận từ các thông tin gốc do khách hàng tạo ra trong phần mềm của Pay2S . Ví dụ: Thông tin phái sinh từ phần mềm quản lý dòng tiền trong kinh doanh có thể là Báo cáo về doanh số trung bình, chi phí trung bình hàng tháng…</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">2. Quyền sử dụng phần mềm</h2>



                        <p>2.1 Khách hàng có quyền sử dụng đầy đủ các tính năng của phần mềm trong thời gian thuê bao còn hạn sử dụng.</p>



                        <p>2.2 Đối với mỗi thuê bao khách hàng đã đăng ký và thanh toán tiền sử dụng thì sẽ được cấp một API Key theo đăng ký của khách hàng để khách hàng sử dụng.</p>



                        <p>2.3 Khách hàng không được phép sử dụng dịch vụ phần mềm bao gồm nhưng không giới hạn bởi việc cập nhật dữ liệu, gửi email, viết bài hoặc truyền tải dữ liệu với mục đích sau:</p>



                        <ul class="wp-block-list">
                            <li>a) Làm tổn hại, làm phiền cho người khác hoặc gây ra thương tổn đến con người và tài sản;</li>



                            <li>b) Liên quan đến việc công bố các thông tin hoặc tài liệu lừa đảo, gây mất uy tín danh dự, quấy rối hoặc mang tính khiêu dâm;</li>



                            <li>c) Xâm phạm các quyền riêng tư hoặc kỳ thị chủng tộc, tôn giáo, giới tính, người tàn tật;</li>



                            <li>d) Xâm phạm quyền sở hữu trí tuệ hoặc các quyền sở hữu khác;</li>



                            <li>e) Cản trở hoặc phá hỏng Dịch vụ (bao gồm nhưng không giới hạn bởi việc truy cập Dịch vụ thông qua bất cứ phương tiện máy móc, phần mềm);</li>



                            <li>f) Vi phạm quy định của pháp luật.</li>
                        </ul>



                        <p>2.4 Trước khi hết hạn, khách hàng cần thực hiện thủ tục gia hạn thuê bao để tiếp tục sử dụng phần mềm. Thời điểm gia hạn là thời điểm tính từ ngày hết hạn của kỳ đăng ký sử dụng trước đó.</p>



                        <p>2.5 Khi hết hạn thuê bao, khách hàng chỉ được đăng nhập vào để sử dụng phần mềm trong vòng 07 ngày kể từ ngày hết hạn. Quá 07 ngày, phần mềm sẽ không cho phép khách hàng đăng nhập vào để sử dụng nữa.</p>



                        <p>2.6 Khi thuê bao quá hạn 30 ngày, nếu khách hàng chưa làm thủ tục đăng ký và thanh toán tiền gia hạn thuê bao thì Pay2S sẽ làm thủ tục cắt thuê bao và xóa bỏ dữ liệu của khách hàng. Khách hàng sẽ không sử dụng được dịch vụ phần mềm Pay2S nữa sau khi Pay2S đã cắt thuê bao.</p>



                        <p>2.7 Khách hàng sẽ bị cắt thuê bao sử dụng phần mềm Pay2S trong vòng 07 ngày kể từ khi nhận được thông báo cắt dịch vụ của Pay2S gửi qua email hoặc hệ thống tự động thông báo trong trường hợp:</p>



                        <ul class="wp-block-list">
                            <li>a) Khách hàng không làm thủ tục đăng ký gia hạn, thanh toán với Pay2S nếu thuê bao quá hạn sử dụng 30 ngày;</li>



                            <li>b) Khách hàng yêu cầu cắt thuê bao phần mềm;</li>



                            <li>c) Khách hàng vi phạm mục đích sử dụng phần mềm được nêu trong thỏa thuận này;</li>



                            <li>d) Khách hàng vi phạm pháp luật và cơ quan công quyền yêu cầu Pay2S ngừng cung cấp dịch vụ thuê bao cho khách hàng.</li>
                        </ul>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">3. Giá cả và phương thức thanh toán</h2>



                        <p>3.1 Lần đầu tiên khi bắt đầu sử dụng phần mềm, khách hàng phải thanh toán phí khởi tạo và phí thuê bao cho Pay2S .</p>



                        <p>3.2 Khách hàng chịu trách nhiệm thanh toán cho Pay2S 100% giá trị của gói sản phẩm/dịch vụ mà khách hàng chọn mua ngay sau khi khách hàng gửi đơn đặt hàng cho Pay2S .</p>



                        <p>3.3 Thời điểm bắt đầu tính phí thuê bao được tính từ ngày Pay2S bàn giao cho khách hàng thông tin truy cập vào phần mềm căn cứ vào email thông báo của Pay2S .</p>



                        <p>3.4 Việc thanh toán phí thuê bao cho kỳ tiếp theo phải được thực hiện trước ngày hết hạn của kỳ thuê bao trước đó. Pay2S sẽ gửi thông báo về việc đóng phí thuê bao trực tiếp trên chính phần mềm Pay2S mà khách hàng đang sử dụng (quy định tại điều 6.5a).</p>



                        <p>3.5 Pay2S có quyền điều chỉnh mức phí thuê bao theo giá thị trường và công bố trực tiếp trên website <a href="https://pay2s.vn">https://pay2s.vn</a>. Trong trường hợp khách hàng đã thanh toán trước phí thuê bao cho nhiều kỳ thì mức phí thuê bao sẽ không thay đổi trong suốt thời hạn thuê bao mà khách hàng đã thanh toán.</p>



                        <p>3.6 Khách hàng chịu trách nhiệm thanh toán cho Pay2S bằng tiền mặt, chuyển khoản hoặc thanh toán trực tuyến thông qua ngân hàng hoặc đối tác thứ ba. Trường hợp thanh toán bằng tiền mặt, khách hàng chỉ thanh toán cho cán bộ của Pay2S khi có đủ giấy tờ: Giấy giới thiệu của công ty về việc nhận tiền mặt (có ghi rõ số tiền), thẻ nhân viên, chứng minh nhân dân của người được ghi trong giấy giới thiệu.</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">4. Bàn giao sản phẩm, dịch vụ</h2>



                        <p>4.1 Pay2S chịu trách nhiệm bàn giao cho khách hàng thông tin truy cập hệ thống qua email. Khi khách hàng nhận được email thì Pay2S coi như đã hoàn thành nghĩa vụ bàn giao sản phẩm của mình cho khách hàng.</p>



                        <p>4.2 Khách hàng chịu trách nhiệm chuẩn bị đầy đủ thiết bị, nhân lực và đường truyền theo đúng khuyến cáo của Pay2S ghi trong phần mềm để tổ chức khai thác, vận hành hệ thống phần mềm.</p>



                        <p>4.3 Khách hàng chịu trách nhiệm tiếp nhận, sử dụng phần mềm đúng theo hướng dẫn và khuyến cáo sử dụng của Pay2S công bố trong phần mềm.</p>



                        <p>4.4 Khi tiếp nhận bàn giao tài khoản truy cập từ Pay2S , khách hàng có trách nhiệm thay đổi mật khẩu ngầm định ngay trong lần đầu tiên sử dụng phần mềm.</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">5. Tư vấn và hỗ trợ khách hàng</h2>



                        <p>5.1 Pay2S chịu trách nhiệm cung cấp dịch vụ tư vấn hỗ trợ cho khách hàng trong suốt quá trình sử dụng thông qua hình thức gọi điện thoại, email, diễn đàn và các hình thức hỗ trợ khác được công bố tại website <a href="https://pay2s.com" target="_blank" rel="noopener">https://pay2s.com</a></p>



                        <p>5.2 Khi sử dụng dịch vụ tư vấn qua hình thức gọi điện thoại, khách hàng chấp nhận trả cước phí điện thoại theo quy định của nhà cung cấp dịch vụ viễn thông.</p>



                        <p>5.3 Các dịch vụ tư vấn hỗ trợ thông qua hình thức khác (như dịch vụ tư vấn hỗ trợ tại các địa điểm theo yêu cầu của khách hàng, dịch vụ tái đào tạo hướng dẫn sử dụng cho khách hàng) sẽ được hai bên thống nhất về chi phí và phương thức cung cấp bằng văn bản bổ sung khi có phát sinh yêu cầu.</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">6. Bảo hành, bảo trì</h2>



                        <p>6.1 Pay2S chịu trách nhiệm đảm bảo điều kiện kỹ thuật để khách hàng có thể sử dụng được phần mềm 24h/ngày và 7 ngày/tuần ngoại trừ thời gian bảo trì, nâng cấp, khắc phục sự cố cho hệ thống. Thời gian ngưng hệ thống để bảo trì hoặc nâng cấp hoặc sao lưu sẽ được Pay2S báo trước lịch thực hiện cho khách hàng. Lịch bảo trì hoặc nâng cấp hoặc sao lưu sẽ thực hiện theo định kỳ hàng ngày hoặc hàng tuần hoặc hàng tháng hoặc hàng năm và ưu tiên vào buổi đêm khi hệ thống ít sử dụng nhất.</p>



                        <p>6.2 Pay2S có trách nhiệm tiến hành khắc phục sự cố của hệ thống chậm nhất là 8h làm việc kể từ khi tiếp nhận được yêu cầu từ người sử dụng của khách hàng.</p>



                        <p>6.3 Pay2S có trách nhiệm cập nhật phiên bản mới nhất của phần mềm cho khách hàng sử dụng trong thời hạn thuê bao mà khách đã đăng ký và thanh toán cho Pay2S.</p>



                        <p>6.4 Khách hàng đồng ý chấp nhận tất cả sự vá lỗi, sửa lỗi, nâng cấp, bảo trì cần thiết để các tính năng của dịch vụ hoạt động chính xác và đảm bảo tính bảo mật của dịch vụ. Trừ trường hợp khẩn cấp, Pay2S sẽ thông báo trước tới khách hàng lịch trình của các hoạt động sửa lỗi, nâng cấp này.</p>



                        <p>6.5 Khi gần hết hạn sử dụng thuê bao phần mềm Pay2S có trách nhiệm thông báo cho khách hàng trực tiếp trên chính phần mềm Pay2S như sau:</p>



                        <ul class="wp-block-list">
                            <li>a) Trong vòng 30 ngày trước ngày hết hạn: Thông báo cho khách hàng biết thời hạn còn lại của thuê bao và hướng dẫn khách hàng thủ tục gia hạn thuê bao;</li>



                            <li>b) Trong vòng 07 ngày sau ngày hết hạn: Thông báo cho khách hàng biết thuê bao đã quá hạn, cảnh báo cho khách hàng biết nếu quá hạn trên 07 ngày thì khách hàng không dùng được phần mềm nữa và hướng dẫn khách hàng thủ tục gia hạn thuê bao;</li>



                            <li>c) Trong vòng từ 08 đến 30 ngày sau ngày hết hạn: Thông báo cho khách hàng biết thuê bao đã quá hạn, khách hàng cần phải gia hạn thì mới tiếp tục sử dụng được phần mềm;</li>



                            <li>d) Quá hạn 30 ngày, Pay2S sẽ thực hiện việc cắt thuê bao và xóa bỏ dữ liệu của khách hàng.</li>
                        </ul>



                        <p>6.6 Khách hàng có thể tự chủ động tra cứu thời hạn sử dụng của thuê bao đã sử dụng ngay trên phần mềm theo tài liệu hướng dẫn sử dụng của phần mềm.</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">7. Ủy quyền truy cập và xử lý dữ liệu</h2>



                        <p><strong>7.1</strong> Bằng việc cung cấp các thông tin xác thực để Pay2S liên kết tài khoản của khách hàng tại các ngân hàng, các tổ chức tín dụng vào hệ thống, khách hàng đã đồng ý ủy quyền cho Pay2S được thay mặt khách hàng truy cập và xử lý các dữ liệu cần thiết cho mục đích cung cấp sản phẩm dịch vụ phần mềm.</p>



                        <p><strong>7.2</strong> Các thông tin Pay2S được ủy quyền truy cập bao gồm nhưng không giới hạn bởi các thông tin sau: Thông tin định danh (tên, số điện thoại, CMND/CCCD), thông tin tài khoản giao dịch (số tài khoản, tên tài khoản, đơn vị tiền tệ, số dư hiện tại), thông tin về biến động số dư.</p>



                        <p><strong>7.3</strong> Mọi truy cập dữ liệu đều ở chế độ chỉ đọc. Pay2S không có quyền và khả năng để sử dụng tiền hay thay đổi tình trạng, thông tin tài khoản của khách hàng.</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">8. Bảo mật</h2>



                        <p><strong>8.1</strong> Pay2S chịu trách nhiệm thực hiện và duy trì tất cả các biện pháp bảo vệ mang tính hành chính, vật lý và kỹ thuật để bảo vệ cho tính bảo mật và toàn vẹn đối với dữ liệu khách hàng. Pay2S cam kết sẽ không:</p>



                        <ul class="wp-block-list">
                            <li>a) Sửa đổi dữ liệu khách hàng mà không có sự đồng ý của khách hàng hoặc không phải vì mục đích khắc phục lỗi hay sự cố;</li>



                            <li>b) Không tiết lộ dữ liệu khách hàng trừ trường hợp được khách hàng cho phép;</li>



                            <li>c) Không truy cập vào dữ liệu và/hoặc làm thay đổi dữ liệu của khách hàng trừ trường hợp khắc phục lỗi kỹ thuật hoặc theo yêu cầu của khách hàng khi sử dụng dịch vụ hỗ trợ.</li>
                        </ul>



                        <p><strong>8.2</strong> Pay2S chịu trách nhiệm bảo mật mọi thông tin về dữ liệu của khách hàng và không được phép tiết lộ cho bất kỳ bên thứ ba nào khác. Pay2S không chịu trách nhiệm về các thất thoát dữ liệu, bí mật thông tin của khách hàng do khách hàng vô tình hoặc cố ý gây ra.</p>



                        <p><strong>8.3</strong> Khách hàng chịu trách nhiệm xác định và xác thực quyền của tất cả những người dùng truy nhập vào dữ liệu của khách hàng.</p>



                        <p><strong>8.4</strong> Khách hàng chịu trách nhiệm đảm bảo bí mật thông tin tài khoản người dùng.</p>



                        <p><strong>8.5</strong> Khách hàng chịu trách nhiệm đối với toàn bộ các hoạt động thực hiện bởi các tài khoản người dùng của khách hàng và có trách nhiệm ngay lập tức thông báo với Pay2S về các truy cập trái phép.</p>



                        <p><strong>8.6</strong> Pay2S sẽ không chịu bất cứ trách nhiệm nào liên quan đến các tổn hại gây ra bởi người dùng của khách hàng, bao gồm các cá nhân không có quyền truy cập vào dịch vụ vẫn có thể lấy được quyền truy cập do lỗi máy tính/ phần mềm hoặc hệ thống mạng nội bộ của khách hàng.</p>



                        <p><strong>8.7</strong> Trong phạm vi của thỏa thuận này, “Thông tin bí mật” bao gồm: Dữ liệu của khách hàng, công nghệ độc quyền của mỗi bên, quy trình nghiệp vụ và các thông tin kỹ thuật của sản phẩm, thiết kế, và toàn bộ quá trình trao đổi giữa hai bên liên quan đến dịch vụ. Bất kể những điều đã đề cập ở trên, “Thông tin bí mật” không bao gồm các thông tin mà:</p>



                        <ul class="wp-block-list">
                            <li>a) Được công chúng biết tới;</li>



                            <li>b) Được biết tới trong ngành trước khi tiết lộ;</li>



                            <li>c) Được công chúng biết tới không phải do lỗi của bên nhận thông tin;</li>



                            <li>d) Dữ liệu tổng hợp trong đó không chứa bất kỳ thông tin cá nhân hoặc thông tin nào cụ thể của khách hàng.</li>
                        </ul>



                        <p><strong>8.8</strong> Khách hàng và Pay2S cùng thỏa thuận:</p>



                        <ul class="wp-block-list">
                            <li>a) Thực hiện các biện pháp cần thiết để giữ bí mật cho tất cả các “Thông tin bí mật”;</li>



                            <li>b) Không sao chép, cung cấp một phần hay toàn bộ thông tin bảo mật cho bất kỳ bên thứ ba khi chưa có sự chấp thuận của bên có quyền sở hữu đối với “Thông tin bí mật”;</li>



                            <li>c) Không sử dụng “Thông tin bí mật” mà các bên đã cung cấp cho nhau phục vụ cho các mục đích khác ngoài mục đích thực hiện thỏa thuận này.</li>
                        </ul>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">9. Bản quyền phần mềm và dữ liệu</h2>



                        <p><strong>9.1</strong> Pay2S là chủ sở hữu và có toàn quyền tác giả phần mềm Pay2S.</p>



                        <p><strong>9.2</strong> Khách hàng có quyền sử dụng phần mềm để tạo ra dữ liệu phục vụ công việc của đơn vị và có quyền tải về phần dữ liệu do chính đơn vị nhập vào hệ thống trong suốt thời gian được cấp thuê bao sử dụng phần mềm.</p>



                        <p><strong>9.3</strong> Khách hàng đồng ý rằng sản phẩm/dịch vụ, bao gồm nhưng không giới hạn: giao diện người sử dụng, đoạn âm thanh, đoạn video, nội dung hướng dẫn sử dụng và phần mềm được sử dụng để thực hiện sản phẩm/dịch vụ thuộc sở hữu riêng của Pay2S được bảo hộ bởi pháp luật về sở hữu trí tuệ và quyền tác giả. Khách hàng thỏa thuận sẽ không sử dụng các thông tin hoặc tài liệu thuộc sở hữu riêng đó theo bất cứ cách thức nào ngoại trừ cho mục đích sử dụng sản phẩm/dịch vụ theo Thỏa thuận này. Không có phần nào trong sản phẩm/dịch vụ có thể được sao chép lại dưới bất kỳ hình thức nào hoặc bằng bất cứ phương tiện nào, trừ khi được cho phép một cách rõ ràng theo các điều khoản này.</p>



                        <p><strong>9.4</strong> Khách hàng đồng ý không sửa đổi, thuê, cho thuê, cho vay, bán, phân phối, hoặc tạo ra các sản phẩm phái sinh dựa trên sản phẩm/dịch vụ theo bất cứ phương cách nào, và không khai thác sản phẩm/dịch vụ theo bất cứ phương thức không được phép nào, bao gồm nhưng không giới hạn ở việc xâm phạm hoặc tạo gánh nặng lên dung lượng của hệ thống mạng.</p>



                        <p><strong>9.5</strong> VIỆC SỬ DỤNG PHẦN MỀM HOẶC BẤT CỨ PHẦN NÀO CỦA SẢN PHẨM/DỊCH VỤ, TRỪ KHI VIỆC SỬ DỤNG SẢN PHẨM/DỊCH VỤ NHƯ ÐƯỢC CHO PHÉP THEO THỎA THUẬN NÀY, ĐỀU BỊ NGHIÊM CẤM VÀ XÂM PHẠM ÐẾN CÁC QUYỀN SỞ HỮU TRÍ TUỆ CỦA NGƯỜI KHÁC, VÀ KHÁCH HÀNG CÓ THỂ PHẢI CHỊU CÁC HÌNH PHẠT DÂN SỰ VÀ HÌNH SỰ, BAO GỒM CẢ VIỆC BỒI THƯỜNG THIỆT HẠI BẰNG TIỀN CÓ THỂ ĐƯỢC ÁP DỤNG ÐỐI VỚI VIỆC XÂM PHẠM QUYỀN TÁC GIẢ.</p>



                        <p><strong>9.6</strong> Pay2S có quyền nhưng không có nghĩa vụ nào trong việc thực hiện các hành động khắc phục nếu như có bất cứ nội dung nào mà khách hàng vi phạm các điều được liệt kê trong thỏa thuận này. Pay2S không có bất kỳ trách nhiệm pháp lý nào đối với khách hàng trong các tình huống Pay2S thực hiện hành động khắc phục. Khách hàng là người duy nhất chịu trách nhiệm về tính chính xác, chất lượng, tính toàn vẹn, hợp pháp, tin cậy và phù hợp đối với tất cả dữ liệu của mình.</p>



                        <p><strong>9.7</strong> Pay2S có thể đề nghị và khách hàng có thể lựa chọn đồng ý sử dụng các tính năng chưa được phát hành rộng rãi và chưa được kiểm duyệt hoàn toàn về mặt chất lượng theo quy trình của Pay2S (các chức năng Beta). Mục đích của việc này là để khách hàng kiểm duyệt và cung cấp phản hồi cho Pay2S. Khách hàng hoàn toàn chịu trách nhiệm về những rủi ro khi sử dụng các chức năng này. Pay2S không đảm bảo về tính đúng đắn, đầy đủ của các chức năng Beta cũng như không chịu trách nhiệm cho các lỗi sai hoặc thiệt hại gây ra do việc sử dụng các chức năng Beta.</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">10. Thông tin/ thông báo</h2>



                        <p>Trong quá trình sử dụng, khách hàng đồng ý nhận các thông tin/ thông báo do Pay2S gửi với nội dung và phương thức như sau:</p>



                        <p><strong>10.1</strong> Nội dung các thông báo bao gồm nhưng không giới hạn bởi các loại thông tin như sau:</p>



                        <ul class="wp-block-list">
                            <li>a) Thông tin về các tính năng mới của sản phẩm</li>



                            <li>b) Thông tin về các phiên bản mới của sản phẩm</li>



                            <li>c) Thông tin về các sản phẩm có liên quan</li>



                            <li>d) Thông tin về nội dung các bài báo hoặc bản tin mà Pay2S cho rằng có thể hữu ích cho khách hàng trong quá trình hoạt động.</li>
                        </ul>



                        <p><strong>10.2</strong> Phương thức gửi thông báo bao gồm nhưng không giới hạn bởi các hình thức sau:</p>



                        <ul class="wp-block-list">
                            <li>a) Thông báo trực tiếp trên màn hình sản phẩm</li>



                            <li>b) Thông báo qua email</li>



                            <li>c) Thông báo qua tin nhắn trên điện thoại di động</li>



                            <li>d) Thông báo qua điện thoại</li>



                            <li>e) Thông báo qua văn bản</li>



                            <li>f) Thông báo bằng cách gặp trao đổi trực tiếp</li>



                            <li>g) Các hình thức thông báo khác</li>
                        </ul>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">11. Thông tin phái sinh</h2>



                        <p>Pay2S được quyền sử dụng thông tin phái sinh từ một phần hoặc toàn bộ thông tin do khách hàng tạo ra khi sử dụng sản phẩm của Pay2S để phục vụ cho các mục đích nghiên cứu cải tiến sản phẩm, thị trường, thói quen tiêu dùng và các mục đích khác có thể mang lại lợi nhuận hoặc không mang lại lợi nhuận. Pay2S cam kết các thông tin phái sinh này không chứa đựng bất kể thông tin cụ thể nào về liên hệ (tên, số điện thoại, …), các giao dịch cụ thể hoặc các bí mật sản xuất kinh doanh (đã mô tả trong mục 1)</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">12. Giới hạn trách nhiệm pháp lý và thực hiện dịch vụ</h2>



                        <p><strong>12.1.</strong> Pay2S không cam đoan, tuyên bố, hoặc bảo ðảm rằng việc khách hàng sử dụng sản phẩm/dịch vụ của Pay2S sẽ không bị gián ðoạn hoặc không bị lỗi, hoặc sản phẩm/dịch vụ sẽ đáp ứng yêu cầu khách hàng hoặc tất cả các lỗi trên phần mềm và/hoặc tài liệu sẽ được sửa hoặc hệ thống tổng thể đảm bảo hoạt động của sản phẩm/dịch vụ phần mềm (bao gồm nhưng không giới hạn: mạng internet, các mạng truyền dẫn khác, mạng nội bộ và các thiết bị của khách hàng) sẽ không có virus hoặc không có thành phần gây hại.</p>



                        <p><strong>12.2.</strong> Pay2S không đảm bảo dưới bất kỳ hình thức nào, dù rõ ràng hay ngầm định về các điều kiện như sự thỏa mãn về chất lượng, phù hợp cho nhu cầu sử dụng đặc thù hoặc không xâm phạm các quyền của bên thứ ba. dịch vụ của Pay2S được cung cấp cho khách hàng dưới dạng “theo hiện trạng – as is” và “có sẵn – as available” cho khách hàng sử dụng. khách hàng sẽ chịu toàn bộ trách nhiệm trong việc xác định xem sản phẩm/dịch vụ hoặc thông tin được tạo ra từ sản phẩm/dịch vụ là đúng đắn và đáp ứng đầy đủ cho mục đích sử dụng của khách hàng.</p>



                        <p><strong>12.3.</strong> Trong bất cứ trường hợp nào Pay2S đều không chịu trách nhiệm về bất kỳ các thiệt hại nào trực tiếp, gián tiếp, ngẫu nhiên, đặc biệt, hậu quả hoặc mang tính chất trừng phạt, bao gồm nhưng không giới hạn ở các thiệt hại do mất doanh thu, lợi nhuận, lợi thế kinh doanh, ngừng việc, mất mát dữ liệu do hậu quả của:</p>



                        <ul class="wp-block-list">
                            <li>a) Việc sử dụng hoặc không thể sử dụng sản phẩm/dịch vụ;</li>



                            <li>b) Bất kỳ các thay đổi nào được thực hiện đối với sản phẩm/dịch vụ;</li>



                            <li>c) Truy cập không được phép hoặc biến đổi các dữ liệu;</li>



                            <li>d) Xóa, sai hỏng, hoặc không lưu trữ dữ liệu có trên hoặc thông qua sản phẩm/dịch vụ;</li>



                            <li>e) Các tuyên bố hay hành vi của bất kỳ bên thứ ba nào đối với sản phẩm/dịch vụ;</li>



                            <li>f)&nbsp;Bất kỳ vấn đề nào khác liên quan đến sản phẩm/dịch vụ.</li>
                        </ul>



                        <p><strong>12.4 </strong>Trong trường hợp sản phẩm của Pay2S có sử dụng dịch vụ của bên thứ ba như thông tin dự báo thời tiết, chứng khoán, tỷ giá, …, Pay2S cam kết không tính phí nhưng không đảm bảo về tính đúng sai của các thông tin trong các ứng dụng/ dịch vụ hoặc nếu bên thứ 3 có cập nhật hệ thống mà dẫn đến mất sự ổn định hoặc ngưng trệ dịch vụ. vì vậy, người dùng phải tự cân nhắc khi sử dụng các dịch vụ này.</p>



                        <p><strong>12.5 </strong>Pay2S được miễn trách nhiệm thực hiện nghĩa vụ được nêu trong thỏa thuận này đối với các trường hợp bất khả kháng ghi trong thỏa thuận này.</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">13. Trách nhiệm xử lý sự cố an ninh</h2>



                        <p><strong>13.1</strong> Trong trường hợp khách hàng phát hiện ra các sự cố an ninh của phần mềm Pay2S, khách hàng có trách nhiệm thông báo ngay với Pay2S. Các sự cố an ninh phần mềm bao gồm nhưng không giới hạn bởi các trường hợp sau:</p>



                        <ul class="wp-block-list">
                            <li>a) Bị mất hoặc thay đổi dữ liệu trên phần mềm mà không biết nguyên nhân.</li>



                            <li>b) Bị gián đoạn không sử dụng được sản phẩm.</li>



                            <li>c) Nghi ngờ bị hacker tấn công.</li>
                        </ul>



                        <p><strong>13.2 </strong>Khi xảy ra sự cố an ninh thông tin liên quan đến sản phẩm Pay2S cung cấp cho khách hàng, Pay2S sẽ có trách nhiệm tổ chức điều tra để xử lý sự cố và khôi phục hoạt động cho khách hàng. Trong quá trình điều tra và khắc phục sự cố, khách hàng phải có trách nhiệm tham gia nếu Pay2S có thể yêu cầu.</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">14. Bất khả kháng</h2>



                        <p>Trong trường hợp bất khả kháng hai bên không có nghĩa vụ phải thực hiện trách nhiệm của mình trong thỏa thuận này. Hai bên nhất trí coi các trường hợp sau là bất khả kháng:</p>



                        <ul class="wp-block-list">
                            <li>a) Thiên tai, địch họa gây cách trở hoặc phá hủy hoặc tắc nghẽn hoặc dừng kết nối đến trung tâm dữ liệu của Pay2S.</li>



                            <li>b) Sự cố mất điện trên diện rộng; Sự cố đứt cáp viễn thông gây tắc nghẽn hoặc ngừng kết nối viễn thông, Internet đến trung tâm dữ liệu của Pay2S.</li>



                            <li>c) Tin tặc (hacker), vi rút máy tính (virus) tấn công vào trung tâm dữ liệu của Pay2S làm ngưng trệ, tắc nghẽn hoặc phá hủy phần mềm và dữ liệu.</li>



                            <li>d) Các sự cố bất khả kháng khác theo quy định của pháp luật.</li>
                        </ul>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">15. Tạm ngừng và chấm dứt thỏa thuận</h2>



                        <p><strong>15.1 </strong>Thỏa thuận này bắt đầu kể từ ngày khách hàng đồng ý và chấm dứt khi tất cả các thuê bao được cấp kèm theo thỏa thuận này hết hạn sử dụng. Đối với trường hợp khách hàng dùng thử sản phẩm/dịch vụ mà không chuyển sang hình thức thuê bao trước khi hết hạn dùng thử, thỏa thuận này sẽ được chấm dứt khi hết hạn dùng thử.</p>



                        <p><strong>15.2 </strong>Pay2S có quyền tạm ngừng việc sử dụng của khách hàng đối với dịch vụ trong các trường hợp sau:</p>



                        <ul class="wp-block-list">
                            <li>a) Khách hàng không thực hiện việc đăng ký gia hạn và thanh toán các khoản chi phí sử dụng sản phẩm/dịch vụ sau khi quá hạn 30 ngày;</li>



                            <li>b) Pay2S cho rằng dịch vụ đang được khách hàng sử dụng để tham gia vào các cuộc tấn công từ chối dịch vụ, gửi thư rác, các hoạt động bất hợp pháp hoặc việc sử dụng sản phẩm/dịch vụ của khách hàng gây nguy hại tới Pay2S và những người khác.</li>
                        </ul>



                        <p><strong>15.3 </strong>Thỏa thuận được coi như chấm dứt trong các trường hợp sau:</p>



                        <ul class="wp-block-list">
                            <li>a) Pay2S đơn phương chấm dứt thỏa thuận do khách hàng không thực hiện nghĩa vụ thanh toán cho Pay2S theo thỏa thuận giữa hai bên;</li>



                            <li>b) Pay2S đơn phương chấm dứt thỏa thuận theo yêu cầu của tòa án và cơ quan có thẩm quyền của nhà nước;</li>



                            <li>c) Khách hàng gửi thông báo yêu cầu chấm dứt thỏa thuận thuê bao cho Pay2S bằng văn bản.</li>
                        </ul>



                        <p><strong>15.4.</strong> Pay2S không có nghĩa vụ hoàn trả bất kể chi phí nào mà khách hàng đã thanh toán trong trường hợp chấm dứt thỏa thuận vì những lý do đã nêu trên. Pay2S chỉ chịu trách nhiệm bảo đảm duy trì dữ liệu của khách hàng trên hệ thống tối đa là 30 ngày kể từ ngày chấm dứt thỏa thuận.</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">16. Căn cứ pháp lý</h2>



                        <p><strong>16.1</strong> Căn cứ Bộ Luật dân sự số 91/2015/QH13</p>



                        <p><strong>16.2 </strong>Căn cứ Luật thương mại nước CHXHCN Việt Nam năm 2005;</p>



                        <p><strong>16.3 </strong>Căn cứ Luật Công nghệ thông tin nước CHXHCN Việt Nam năm 2006</p>



                        <p><strong>16.4 </strong>Căn cứ vào nhu cầu của hai bên.</p>



                        <h2 class="wp-block-heading" style="font-size:1.3rem">17. Điều khoản chung</h2>



                        <p><strong>17.1 </strong>Trong quá trình thực hiện thỏa thuận nếu có vấn đề gì nảy sinh thì hai bên sẽ cùng bàn bạc, thống nhất và tìm giải pháp khắc phục.</p>



                        <p><strong>17.2</strong> Trong trường hợp nảy sinh tranh chấp mà hai bên không thể cùng nhau thương lượng giải quyết được thì hai bên cùng thống nhất mang ra Tòa án TP Hồ Chí Minh để giải quyết.</p>
                    </div>
                </div>
            </div>
            <div class="col-xxl-4 col-lg-4">
                <aside class="sidebar-area">
                    <div class="widget widget_banner">
                        <h4 class="widget_title">Tài liệu tích hợp</h4>
                        <div class="download-widget-wrap">
                            <a href="https://docs.pay2s.vn" class="th-btn"><i class="fa-light fa-file-pdf me-2"></i>Xem Docs</a>
                        </div>
                    </div>
                    <div class="widget widget_banner">
                        <div class="widget-banner">
                            <span class="text">LIÊN HỆ NGAY</span>
                            <h2 class="title">Bạn có thắc mắc?</h2>
                            <a href="/lien-he" class="th-btn style3">Liên hệ<i class="fas fa-arrow-right ms-2"></i></a>
                        </div>
                    </div>
                </aside>
            </div>
        </div>
    </div>
</section>
<?php include 'footer.php'; ?>