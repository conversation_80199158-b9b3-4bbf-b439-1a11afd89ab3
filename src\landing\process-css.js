// <PERSON><PERSON><PERSON> để process CSS từ fe-assets và thêm prefix .landing-container
const fs = require('fs');
const path = require('path');

const cssFiles = [
  '../fe-assets/css/bootstrap.min.css',
  '../fe-assets/css/fontawesome.min.css', 
  '../fe-assets/css/magnific-popup.min.css',
  '../fe-assets/css/swiper-bundle.min.css',
  '../fe-assets/css/style.css'
];

let combinedCSS = `/* Landing Page CSS - Auto-generated */\n\n`;

cssFiles.forEach(file => {
  try {
    const cssContent = fs.readFileSync(path.join(__dirname, file), 'utf8');
    
    // Thêm prefix .landing-container cho tất cả CSS rules
    const prefixedCSS = cssContent
      // Thêm prefix cho các selector
      .replace(/([^{}]+){/g, (match, selector) => {
        // Bỏ qua @media, @keyframes, @import
        if (selector.trim().startsWith('@')) {
          return match;
        }
        
        // Split multiple selectors
        const selectors = selector.split(',').map(s => {
          const trimmed = s.trim();
          if (trimmed.startsWith(':root') || trimmed.startsWith('html') || trimmed.startsWith('body')) {
            return `.landing-container ${trimmed}`;
          }
          return `.landing-container ${trimmed}`;
        }).join(', ');
        
        return `${selectors} {`;
      });
    
    combinedCSS += `\n/* From ${file} */\n${prefixedCSS}\n`;
  } catch (error) {
    console.error(`Error reading ${file}:`, error.message);
  }
});

// Write combined CSS
fs.writeFileSync(path.join(__dirname, 'landing.css'), combinedCSS);
console.log('CSS processing completed!');
