import React from 'react';
import LandingLayout from '../LandingLayout';
import Header from '../components/Header';
import Footer from '../components/Footer';

const PricingPage = () => {
  return (
    <LandingLayout>
      <Header />
      
      <h1 style={{ display: 'none' }}>Bảng giá dịch vụ Pay2S</h1>
      
      {/* Breadcrumb */}
      <div className="breadcumb-wrapper" data-bg-src="/fe-assets/img/bg/breadcumb-bg.jpg">
        <div className="container">
          <div className="breadcumb-content">
            <h1 className="breadcumb-title">Bảng giá</h1>
            <ul className="breadcumb-menu">
              <li><a href="/">Trang chủ</a></li>
              <li>Bảng giá</li>
            </ul>
          </div>
        </div>
      </div>
      
      {/* Pricing Content */}
      <div className="space">
        <div className="container">
          <div className="row">
            <div className="col-12 text-center">
              <h2>Bảng giá dịch vụ</h2>
              <p>Bảng giá chi tiết sẽ được thêm vào đây...</p>
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </LandingLayout>
  );
};

export default PricingPage;
