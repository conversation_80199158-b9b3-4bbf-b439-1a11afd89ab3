import React, { useEffect, useState, useMemo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import * as XLSX from "xlsx";
import { Icon } from "@iconify/react/dist/iconify.js";
import DatePicker from "react-datepicker";
import useBankApi from "../../callapi/Bank.jsx";

import "react-datepicker/dist/react-datepicker.css";

// Import hình ảnh logo ngân hàng
import bgAcb from "../../assets/images/banks/acb.jpg";
import bgBidv from "../../assets/images/banks/bidv.jpg";
import bgMbb from "../../assets/images/banks/mbb.jpg";
import bgMomo from "../../assets/images/banks/momo.jpg";
import bgSeab from "../../assets/images/banks/seab.jpg";
import bgTcb from "../../assets/images/banks/tcb.jpg";
import bgTpb from "../../assets/images/banks/tpb.jpg";
import bgVcb from "../../assets/images/banks/vcb.jpg";
import bgVtb from "../../assets/images/banks/vtb.jpg";

// Component chính
const TransactionBank = () => {
  const [transactions, setTransactions] = useState([]);
  const {
    data: apiData,
    loading,
    error,
    callApi: getBankTransactions,
  } = useBankApi();

  const [accountFilter, setAccountFilter] = useState("");
  const [bankFilter, setBankFilter] = useState("");
  const [typeFilter, setTypeFilter] = useState("");
  const [dateFrom, setDateFrom] = useState(null);
  const [dateTo, setDateTo] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [selectedRows, setSelectedRows] = useState(new Set()); // State để quản lý các hàng được chọn

  const location = useLocation();
  const navigate = useNavigate();

  const bankLogoMap = {
    ACB: bgAcb,
    BIDV: bgBidv,
    MBB: bgMbb,
    MOMO: bgMomo,
    SEAB: bgSeab,
    TCB: bgTcb,
    TPB: bgTpb,
    VCB: bgVcb,
    VTB: bgVtb,
  };

  const bankNameMap = {
    ACB: "Ngân hàng ACB",
    BIDV: "Ngân hàng BIDV",
    MBB: "Ngân hàng MB Bank",
    MOMO: "Ví điện tử MoMo",
    SEAB: "Ngân hàng SEAB",
    TCB: "Ngân hàng Techcombank",
    TPB: "Ngân hàng TPBank",
    VCB: "Ngân hàng Vietcombank",
    VTB: "Ngân hàng VietinBank",
  };

  // Helper function để tạo danh sách tài khoản cho dropdown
  const getAccountOptions = useMemo(() => {
    if (!transactions.length) return [];
    const accountMap = new Map();
    transactions.forEach((trans) => {
      if (trans.accountNumber) {
        if (!accountMap.has(trans.accountNumber)) {
          accountMap.set(trans.accountNumber, {
            number: trans.accountNumber,
            isVA: false,
            bankName: trans.shortBankName,
          });
        }
      }
      if (trans.vaNumber && trans.vaNumber !== trans.accountNumber) {
        if (!accountMap.has(trans.vaNumber)) {
          accountMap.set(trans.vaNumber, {
            number: trans.vaNumber,
            isVA: true,
            bankName: trans.shortBankName,
            parentAccount: trans.accountNumber,
          });
        }
      }
    });
    return Array.from(accountMap.values()).sort((a, b) => {
      if (a.isVA !== b.isVA) return a.isVA ? 1 : -1;
      return a.number.localeCompare(b.number);
    });
  }, [transactions]);

  const getBankLogo = (shortName) => bankLogoMap[shortName] || null;
  const getBankFullName = (shortName) => bankNameMap[shortName] || shortName;
  const typeMap = { IN: "Tiền vào", OUT: "Tiền ra" };

  const fetchAndSetTransactions = React.useCallback(() => {
    const userId = localStorage.getItem("user_id");
    if (userId) {
      getBankTransactions({ action: "list_transactions", user_id: userId });
    }
  }, [getBankTransactions]);

  useEffect(() => {
    fetchAndSetTransactions();
  }, [fetchAndSetTransactions]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const accountNumberFromUrl = params.get("accountNumber");
    const bankNameFromUrl = params.get("bankName");
    const vaNumberFromUrl = params.get("vaNumber");
    if (vaNumberFromUrl) {
      setAccountFilter(vaNumberFromUrl);
    } else if (accountNumberFromUrl) {
      setAccountFilter(accountNumberFromUrl);
    }
    if (bankNameFromUrl) {
      setBankFilter(bankNameFromUrl.toUpperCase());
    }
  }, [location.search]);

  const updateUrlParams = React.useCallback(
    (newAccountFilter, newBankFilter) => {
      const currentParams = new URLSearchParams(window.location.search);
      if (newAccountFilter) {
        currentParams.set("accountNumber", newAccountFilter);
      } else {
        currentParams.delete("accountNumber");
      }
      if (newBankFilter) {
        currentParams.set("bankName", newBankFilter);
      } else {
        currentParams.delete("bankName");
      }
      currentParams.delete("vaNumber");
      const newUrl = `${window.location.pathname}${
        currentParams.toString() ? `?${currentParams.toString()}` : ""
      }`;
      navigate(newUrl, { replace: true });
    },
    [navigate]
  );

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const currentAccountParam =
      params.get("accountNumber") || params.get("vaNumber") || "";
    const currentBankParam = (params.get("bankName") || "").toUpperCase();
    const shouldUpdate =
      accountFilter !== currentAccountParam || bankFilter !== currentBankParam;
    if (shouldUpdate && (accountFilter || bankFilter)) {
      const timeoutId = setTimeout(() => {
        updateUrlParams(accountFilter, bankFilter);
      }, 100);
      return () => clearTimeout(timeoutId);
    }
  }, [accountFilter, bankFilter, location.search, updateUrlParams]);

  useEffect(() => {
    setCurrentPage(1);
  }, [
    itemsPerPage,
    accountFilter,
    bankFilter,
    typeFilter,
    dateFrom,
    dateTo,
    searchTerm,
  ]);

  useEffect(() => {
    setSelectedRows(new Set());
  }, [accountFilter, bankFilter, typeFilter, dateFrom, dateTo, searchTerm]);

  const handleAccountFilterChange = (newValue) => setAccountFilter(newValue);
  const handleBankFilterChange = (newValue) => setBankFilter(newValue);

  useEffect(() => {
    if (apiData?.transactions && Array.isArray(apiData.transactions)) {
      setTransactions(apiData.transactions);
    }
  }, [apiData]);

  const filteredData = useMemo(() => {
    return transactions.filter((trans) => {
      const matchAccount =
        !accountFilter ||
        trans.accountNumber === accountFilter ||
        trans.vaNumber === accountFilter;
      const matchBank = !bankFilter || trans.shortBankName === bankFilter;
      const matchType = !typeFilter || trans.type === typeFilter;
      const transDate = new Date(trans.transactionDate.replace(" ", "T"));
      const fromDate = dateFrom ? new Date(dateFrom) : null;
      const toDate = dateTo ? new Date(dateTo) : null;
      if (fromDate) fromDate.setHours(0, 0, 0, 0);
      if (toDate) toDate.setHours(23, 59, 59, 999);
      const matchDate =
        (!fromDate || transDate >= fromDate) &&
        (!toDate || transDate <= toDate);
      const term = searchTerm.toLowerCase();
      const matchSearch =
        !term ||
        String(trans.id).toLowerCase().includes(term) ||
        trans.description.toLowerCase().includes(term);
      return matchAccount && matchBank && matchType && matchSearch && matchDate;
    });
  }, [
    transactions,
    accountFilter,
    bankFilter,
    typeFilter,
    dateFrom,
    dateTo,
    searchTerm,
  ]);

  const summaryData = useMemo(() => {
    const incoming = filteredData.filter((t) => t.type === "IN");
    const outgoing = filteredData.filter((t) => t.type === "OUT");
    const totalIncoming = incoming.reduce((sum, t) => sum + t.amount, 0);
    const totalOutgoing = outgoing.reduce((sum, t) => sum + t.amount, 0);
    return {
      total: {
        amount: totalIncoming - totalOutgoing,
        count: filteredData.length,
      },
      incoming: { amount: totalIncoming, count: incoming.length },
      outgoing: { amount: totalOutgoing, count: outgoing.length },
    };
  }, [filteredData]);

  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filteredData.slice(startIndex, startIndex + itemsPerPage);
  }, [filteredData, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredData.length / itemsPerPage);

  const formatCurrency = (amount, type = null) => {
    const amountAbs = Math.abs(amount || 0);
    const formatted = new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amountAbs);
    if (type === "IN") return `+ ${formatted}`;
    if (type === "OUT") return `- ${formatted}`;
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount || 0);
  };

  const handleResetFilters = () => {
    setAccountFilter("");
    setBankFilter("");
    setTypeFilter("");
    setDateFrom(null);
    setDateTo(null);
    setCurrentPage(1);
    setSearchTerm("");
    setSelectedRows(new Set());
  };

  // Lấy dữ liệu dựa trên lựa chọn của người dùng hoặc toàn bộ dữ liệu đã lọc
  const getDataForAction = () => {
    // Nếu có hàng được chọn, chỉ lấy những hàng đó
    if (selectedRows.size > 0) {
      return filteredData.filter((trans) => selectedRows.has(trans.id));
    }
    // Nếu không có hàng nào được chọn, lấy toàn bộ dữ liệu đã lọc
    return filteredData;
  };

  const exportExcel = () => {
    const dataToProcess = getDataForAction();
    if (dataToProcess.length === 0) {
      alert("Không có dữ liệu để xuất file Excel.");
      return;
    }

    const dataToExport = dataToProcess.map((trans, index) => ({
      STT: index + 1,
      "ID Giao Dịch": String(trans.id),
      "Ngân Hàng": getBankFullName(trans.shortBankName),
      "Số Tài Khoản": trans.accountNumber,
      "Số Tiền (VND)": trans.type === "IN" ? trans.amount : -trans.amount,
      "Loại Giao Dịch": typeMap[trans.type],
      "Thời Gian Giao Dịch": trans.transactionDate,
      "Nội Dung Giao Dịch": trans.description,
      "Hóa Đơn Liên Quan": trans.invoiceRelated || "-",
    }));

    const ws = XLSX.utils.json_to_sheet(dataToExport);
    ws["!cols"] = [
      { wch: 5 },
      { wch: 25 },
      { wch: 20 },
      { wch: 20 },
      { wch: 18 },
      { wch: 15 },
      { wch: 20 },
      { wch: 50 },
      { wch: 20 },
    ];
    const moneyFormat = '#,##0 "VND"';
    for (let i = 2; i <= dataToExport.length + 1; i++) {
      const cellRef = XLSX.utils.encode_cell({ c: 4, r: i - 1 });
      if (ws[cellRef]) ws[cellRef].z = moneyFormat;
    }

    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Lich Su Giao Dich");
    const today = new Date();
    const fileName = `Lich_Su_Giao_Dich_${today.getDate()}-${
      today.getMonth() + 1
    }-${today.getFullYear()}.xlsx`;
    XLSX.writeFile(wb, fileName);
  };

  const handlePrint = () => {
    const dataToProcess = getDataForAction();
    if (dataToProcess.length === 0) {
      alert("Không có dữ liệu để in.");
      return;
    }
    const tableRows = dataToProcess
      .map(
        (trans, index) => `
        <tr>
          <td style="text-align: center;">${index + 1}</td>
          <td>${trans.id}</td>
          <td>
            ${getBankFullName(trans.shortBankName)} - ${trans.accountNumber}
            ${
              trans.vaNumber && trans.vaNumber !== trans.accountNumber
                ? `<br><small>VA: ${trans.vaNumber}</small>`
                : ""
            }
          </td>
          <td style="text-align: right; color: ${
            trans.type === "IN" ? "green" : "red"
          };">
            ${formatCurrency(trans.amount, trans.type)}
          </td>
          <td style="text-align: center;">${typeMap[trans.type]}</td>
          <td>${trans.transactionDate}</td>
          <td>${trans.description}</td>
          <td>${trans.invoiceRelated || "-"}</td>
        </tr>
      `
      )
      .join("");
    const printContent = `
      <html>
        <head>
          <title>Lịch sử Giao dịch</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 12px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; word-break: break-word; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .header { text-align: center; margin-bottom: 20px; }
          </style>
        </head>
        <body>
          <div class="header">
            <h2>Lịch sử Giao dịch</h2>
            <p>Ngày xuất: ${new Date().toLocaleString("vi-VN")}</p>
            <p>Tổng số giao dịch: ${dataToProcess.length}</p>
          </div>
          <table>
            <thead>
              <tr>
                <th style="width: 40px; text-align: center;">STT</th>
                <th style="width: 150px;">ID Giao dịch</th>
                <th style="width: 150px;">Tài khoản</th>
                <th style="width: 120px; text-align: right;">Số tiền</th>
                <th style="width: 80px; text-align: center;">Loại</th>
                <th style="width: 140px;">Thời gian</th>
                <th>Nội dung</th>
                <th style="width: 120px;">Hóa đơn</th>
              </tr>
            </thead>
            <tbody>${tableRows}</tbody>
          </table>
        </body>
      </html>
    `;
    const printWindow = window.open("", "_blank");
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  };

  // Hàm xử lý chọn/bỏ chọn một hàng
  const handleSelectRow = (rowId) => {
    const newSelectedRows = new Set(selectedRows);
    if (newSelectedRows.has(rowId)) {
      newSelectedRows.delete(rowId);
    } else {
      newSelectedRows.add(rowId);
    }
    setSelectedRows(newSelectedRows);
  };

  // Hàm xử lý chọn/bỏ chọn tất cả các hàng trên trang hiện tại
  const handleSelectAll = () => {
    const currentIds = paginatedData.map((row) => row.id);
    const newSelectedRows = new Set(selectedRows);
    const allOnPageSelected =
      currentIds.length > 0 && currentIds.every((id) => selectedRows.has(id));

    if (allOnPageSelected) {
      currentIds.forEach((id) => newSelectedRows.delete(id));
    } else {
      currentIds.forEach((id) => newSelectedRows.add(id));
    }
    setSelectedRows(newSelectedRows);
  };

  // Kiểm tra xem tất cả hàng trên trang hiện tại có được chọn không
  const isAllOnPageSelected = useMemo(() => {
    if (paginatedData.length === 0) return false;
    return paginatedData.every((row) => selectedRows.has(row.id));
  }, [paginatedData, selectedRows]);

  const Pagination = () => {
    if (totalPages <= 1) return null;
    const pageNumbers = [];
    const maxPagesToShow = 5;
    if (totalPages <= maxPagesToShow + 2) {
      for (let i = 1; i <= totalPages; i++) pageNumbers.push(i);
    } else {
      pageNumbers.push(1);
      if (currentPage > 3) pageNumbers.push("...");
      let start = Math.max(2, currentPage - 1);
      let end = Math.min(totalPages - 1, currentPage + 1);
      if (currentPage <= 2) end = 3;
      if (currentPage >= totalPages - 1) start = totalPages - 2;
      for (let i = start; i <= end; i++) pageNumbers.push(i);
      if (currentPage < totalPages - 2) pageNumbers.push("...");
      pageNumbers.push(totalPages);
    }

    return (
      <nav aria-label="pagination">
        <ul className="pagination mb-0">
          <li className={`page-item ${currentPage === 1 ? "disabled" : ""}`}>
            <button
              className="page-link"
              onClick={() => setCurrentPage((c) => c - 1)}
            >
              &laquo;
            </button>
          </li>
          {pageNumbers.map((num, index) =>
            num === "..." ? (
              <li key={`ellipsis-${index}`} className="page-item disabled">
                <span className="page-link">...</span>
              </li>
            ) : (
              <li
                key={num}
                className={`page-item ${currentPage === num ? "active" : ""}`}
              >
                <button
                  className="page-link"
                  onClick={() => setCurrentPage(num)}
                >
                  {num}
                </button>
              </li>
            )
          )}
          <li
            className={`page-item ${
              currentPage === totalPages ? "disabled" : ""
            }`}
          >
            <button
              className="page-link"
              onClick={() => setCurrentPage((c) => c + 1)}
            >
              &raquo;
            </button>
          </li>
        </ul>
      </nav>
    );
  };

  return (
    <>
      <div className="container-fluid mt-4">
        <div className="card">
          <div className="card-body">
            <div className="row g-3 align-items-center">
              <div className="col-md-2">
                <select
                  className="form-select"
                  value={accountFilter}
                  onChange={(e) => handleAccountFilterChange(e.target.value)}
                >
                  <option value="">Tất cả tài khoản</option>
                  {getAccountOptions.map((acc) => (
                    <option key={acc.number} value={acc.number}>
                      {acc.number}{" "}
                      {acc.isVA
                        ? `(VA - ${getBankFullName(acc.bankName)})`
                        : `(${getBankFullName(acc.bankName)})`}
                    </option>
                  ))}
                </select>
              </div>
              <div className="col-md-2">
                <select
                  className="form-select"
                  value={bankFilter}
                  onChange={(e) => handleBankFilterChange(e.target.value)}
                >
                  <option value="">Tất cả ngân hàng</option>
                  {[...new Set(transactions.map((t) => t.shortBankName))]
                    .filter(Boolean)
                    .sort()
                    .map((bank) => (
                      <option key={bank} value={bank}>
                        {getBankFullName(bank)}
                      </option>
                    ))}
                </select>
              </div>
              <div className="col-md-2">
                <select
                  className="form-select"
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  <option value="">Tất cả loại</option>
                  {Object.entries(typeMap).map(([key, value]) => (
                    <option key={key} value={key}>
                      {value}
                    </option>
                  ))}
                </select>
              </div>
              <div className="col-md-4">
                <div className="input-group" style={{ flexWrap: "nowrap" }}>
                  <DatePicker
                    selected={dateFrom}
                    onChange={(date) => setDateFrom(date)}
                    selectsStart
                    startDate={dateFrom}
                    endDate={dateTo}
                    className="form-control"
                    placeholderText="Từ ngày"
                    dateFormat="dd/MM/yyyy"
                    isClearable
                    autoComplete="off"
                  />
                  <span className="input-group-text bg-light border-0">
                    <Icon icon="iconoir:arrow-right" className="text-muted" />
                  </span>
                  <DatePicker
                    selected={dateTo}
                    onChange={(date) => setDateTo(date)}
                    selectsEnd
                    startDate={dateFrom}
                    endDate={dateTo}
                    minDate={dateFrom}
                    className="form-control"
                    placeholderText="Đến ngày"
                    dateFormat="dd/MM/yyyy"
                    isClearable
                    autoComplete="off"
                  />
                </div>
              </div>
              <div className="col-md-2">
                <button
                  className="btn btn-primary w-100 position-relative"
                  onClick={handleResetFilters}
                >
                  <Icon icon="mdi:filter-off" className="me-1" />
                  Chọn lại
                  {(accountFilter ||
                    bankFilter ||
                    typeFilter ||
                    dateFrom ||
                    dateTo ||
                    searchTerm) && (
                    <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                      {
                        [
                          accountFilter,
                          bankFilter,
                          typeFilter,
                          dateFrom,
                          dateTo,
                          searchTerm,
                        ].filter(Boolean).length
                      }
                    </span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>

        <div className="row g-3 mt-1">
          <div className="col-md-4">
            <div className="card shadow-sm h-100">
              <div
                className="card-header text-center p-2 fw-medium"
                style={{ backgroundColor: "#dcfce7", color: "#166534" }}
              >
                Tổng tiền vào
              </div>
              <div className="card-body p-3">
                <div className="row align-items-center text-center">
                  <div className="col-6 border-end">
                    <small className="text-muted">Giá trị</small>
                    <p className="fw-bold mb-0 text-success">
                      {formatCurrency(summaryData.incoming.amount, "IN")}
                    </p>
                  </div>
                  <div className="col-6">
                    <small className="text-muted">Số lượng</small>
                    <p className="fw-bold mb-0">{summaryData.incoming.count}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card shadow-sm h-100">
              <div
                className="card-header text-center p-2 fw-medium"
                style={{ backgroundColor: "#fee2e2", color: "#991b1b" }}
              >
                Tổng tiền ra
              </div>
              <div className="card-body p-3">
                <div className="row align-items-center text-center">
                  <div className="col-6 border-end">
                    <small className="text-muted">Giá trị</small>
                    <p className="fw-bold mb-0 text-danger">
                      {formatCurrency(summaryData.outgoing.amount, "OUT")}
                    </p>
                  </div>
                  <div className="col-6">
                    <small className="text-muted">Số lượng</small>
                    <p className="fw-bold mb-0">{summaryData.outgoing.count}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="col-md-4">
            <div className="card shadow-sm h-100">
              <div
                className="card-header text-center p-2 fw-medium"
                style={{ backgroundColor: "#eef2ff", color: "#3730a3" }}
              >
                Chênh lệch
              </div>
              <div className="card-body p-3">
                <div className="row align-items-center text-center">
                  <div className="col-6 border-end">
                    <small className="text-muted">Giá trị</small>
                    <p className="fw-bold mb-0">
                      {formatCurrency(summaryData.total.amount)}
                    </p>
                  </div>
                  <div className="col-6">
                    <small className="text-muted">Tổng giao dịch</small>
                    <p className="fw-bold mb-0">{summaryData.total.count}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="card mt-3 printable-area">
          <div className="card-body">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <div className="d-flex align-items-center">
                <input
                  type="text"
                  className="form-control"
                  placeholder="Tìm kiếm ID, nội dung..."
                  style={{ maxWidth: "300px" }}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div>
                {selectedRows.size > 0 && (
                  <span className="me-3 text-primary fw-bold">
                    Đã chọn: {selectedRows.size}
                  </span>
                )}
                <button
                  className="btn btn-outline-success ms-2"
                  onClick={exportExcel}
                >
                  <Icon icon="mdi:file-excel-outline" /> Excel
                </button>
                <button
                  className="btn btn-outline-secondary ms-2"
                  onClick={handlePrint}
                >
                  <Icon icon="mdi:printer-outline" /> Print
                </button>
              </div>
            </div>

            {loading ? (
              <p className="text-center p-5">Đang tải...</p>
            ) : error ? (
              <div className="alert alert-danger">{error}</div>
            ) : (
              <div className="table-responsive">
                <table className="table table-hover basic-border-table mb-0">
                  <thead className="table-light">
                    <tr>
                      <th style={{ width: "60px" }} className="text-center">
                        <input
                          type="checkbox"
                          className="form-check-input"
                          checked={isAllOnPageSelected}
                          onChange={handleSelectAll}
                          disabled={paginatedData.length === 0}
                          title="Chọn/Bỏ chọn tất cả trên trang này"
                        />
                      </th>
                      <th style={{ width: "50px" }} className="text-center">
                        STT
                      </th>
                      <th>ID</th>
                      <th>Tài khoản</th>
                      <th>Số tiền</th>
                      <th>Loại</th>
                      <th>Thời gian</th>
                      <th>Nội dung</th>
                      <th>Hóa đơn</th>
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedData.map((trans, index) => {
                      const logoSrc = getBankLogo(trans.shortBankName);
                      const isSelected = selectedRows.has(trans.id);
                      return (
                        <tr
                          key={trans.id}
                          className={isSelected ? "table-primary" : ""}
                          onClick={() => handleSelectRow(trans.id)}
                          style={{ cursor: "pointer" }}
                        >
                          <td
                            className="text-center"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <input
                              type="checkbox"
                              className="form-check-input"
                              checked={isSelected}
                              onChange={() => handleSelectRow(trans.id)}
                            />
                          </td>
                          <td className="text-center">
                            {(currentPage - 1) * itemsPerPage + index + 1}
                          </td>
                          <td
                            className="text-muted"
                            style={{ fontSize: "80%", wordBreak: "break-word" }}
                          >
                            {trans.id}
                          </td>
                          <td>
                            <div className="d-flex align-items-center">
                              {logoSrc && (
                                <img
                                  src={logoSrc}
                                  alt={trans.bankName}
                                  style={{
                                    height: "40px",
                                    marginRight: "10px",
                                    borderRadius: "4px",
                                  }}
                                />
                              )}
                              <div>
                                <small className="fw-bold d-block">
                                  {trans.accountNumber}
                                </small>
                                {trans.vaNumber &&
                                  trans.vaNumber !== trans.accountNumber && (
                                    <small className="text-primary d-block">
                                      VA: {trans.vaNumber}
                                    </small>
                                  )}
                              </div>
                            </div>
                          </td>
                          <td
                            className={`fw-bold ${
                              trans.type === "IN"
                                ? "text-success"
                                : "text-danger"
                            }`}
                          >
                            {formatCurrency(trans.amount, trans.type)}
                          </td>
                          <td>
                            <span
                              className={`badge ${
                                trans.type === "IN"
                                  ? "bg-success-focus text-success-main"
                                  : "bg-danger-focus text-danger-main"
                              }`}
                            >
                              {typeMap[trans.type]}
                            </span>
                          </td>
                          <td>{trans.transactionDate}</td>
                          <td
                            style={{
                              minWidth: "250px",
                              maxWidth: "400px",
                              whiteSpace: "normal",
                              wordBreak: "break-word",
                            }}
                          >
                            {trans.description}
                          </td>
                          <td>{trans.invoiceRelated || "-"}</td>
                        </tr>
                      );
                    })}
                    {paginatedData.length === 0 && (
                      <tr>
                        <td colSpan="9" className="text-center py-4">
                          Không có giao dịch nào.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            {!loading && !error && filteredData.length > 0 && (
              <div className="d-flex justify-content-between align-items-center mt-3">
                <div className="d-flex align-items-center gap-2">
                  <span className="text-muted">Hiển thị</span>
                  <select
                    className="form-select form-select-sm"
                    style={{ width: "auto" }}
                    value={itemsPerPage}
                    onChange={(e) => {
                      setItemsPerPage(Number(e.target.value));
                    }}
                  >
                    <option value="5">5</option>
                    <option value="10">10</option>
                    <option value="20">20</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                  </select>
                  <span className="text-muted">
                    trên {filteredData.length} giao dịch
                  </span>
                </div>
                {totalPages > 1 && <Pagination />}
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default TransactionBank;
