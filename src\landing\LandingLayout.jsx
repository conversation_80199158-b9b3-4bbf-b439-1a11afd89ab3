import React, { useEffect } from "react";
import "./landing.css"; // CSS được scope với .landing-container

const LandingLayout = ({ children }) => {
  useEffect(() => {
    // Load CSS files directly
    const cssFiles = [
      "/fe-assets/css/bootstrap.min.css",
      "/fe-assets/css/fontawesome.min.css",
      "/fe-assets/css/magnific-popup.min.css",
      "/fe-assets/css/swiper-bundle.min.css",
      "/fe-assets/css/style.css",
    ];

    const loadedStyles = [];

    cssFiles.forEach((href) => {
      const link = document.createElement("link");
      link.rel = "stylesheet";
      link.href = href;
      link.setAttribute("data-landing-css", "true");
      document.head.appendChild(link);
      loadedStyles.push(link);
    });
    // Load các script c<PERSON>n thiết cho landing page
    const scripts = [
      "/fe-assets/js/vendor/jquery-3.7.1.min.js",
      "/fe-assets/js/swiper-bundle.min.js",
      "/fe-assets/js/bootstrap.min.js",
      "/fe-assets/js/jquery.magnific-popup.min.js",
      "/fe-assets/js/jquery.counterup.min.js",
      "/fe-assets/js/circle-progress.js",
      "/fe-assets/js/jquery-ui.min.js",
      "/fe-assets/js/imagesloaded.pkgd.min.js",
      "/fe-assets/js/isotope.pkgd.min.js",
      "/fe-assets/js/tilt.jquery.min.js",
      "/fe-assets/js/gsap.min.js",
      "/fe-assets/js/ScrollTrigger.min.js",
      "/fe-assets/js/smooth-scroll.js",
      "/fe-assets/js/particles.min.js",
      "/fe-assets/js/particles-config.js",
      "/fe-assets/js/main.js",
    ];

    const loadedScripts = [];

    scripts.forEach((src) => {
      const script = document.createElement("script");
      script.src = src;
      script.async = true;
      script.setAttribute("data-landing-js", "true"); // Đánh dấu để cleanup
      document.body.appendChild(script);
      loadedScripts.push(script);
    });

    // Cleanup function để remove styles và scripts khi component unmount
    return () => {
      // Remove CSS
      loadedStyles.forEach((link) => {
        if (document.head.contains(link)) {
          document.head.removeChild(link);
        }
      });

      // Remove scripts
      loadedScripts.forEach((script) => {
        if (document.body.contains(script)) {
          document.body.removeChild(script);
        }
      });

      // Remove any remaining landing assets
      document
        .querySelectorAll("[data-landing-css]")
        .forEach((el) => el.remove());
      document
        .querySelectorAll("[data-landing-js]")
        .forEach((el) => el.remove());
    };
  }, []);

  return (
    <div className="landing-container">
      <div className="cursor"></div>
      <div className="cursor2"></div>
      {children}

      {/* Back to top button */}
      <div className="scroll-top">
        <svg
          className="progress-circle svg-content"
          width="100%"
          height="100%"
          viewBox="-1 -1 102 102"
        >
          <path
            d="m50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"
            style={{
              transition: "stroke-dashoffset 10ms linear 0s",
              strokeDasharray: "307.919, 307.919",
              strokeDashoffset: "307.919",
            }}
          ></path>
        </svg>
      </div>
    </div>
  );
};

export default LandingLayout;
