import React, { useEffect } from "react";
import "./landing.css"; // CSS được scope với .landing-container
import LandingCSSLoader from "./LandingCSSLoader";

const LandingLayout = ({ children }) => {
  useEffect(() => {
    // Load các script cần thiết cho landing page
    const scripts = [
      "/src/fe-assets/js/vendor/jquery-3.7.1.min.js",
      "/src/fe-assets/js/swiper-bundle.min.js",
      "/src/fe-assets/js/bootstrap.min.js",
      "/src/fe-assets/js/jquery.magnific-popup.min.js",
      "/src/fe-assets/js/jquery.counterup.min.js",
      "/src/fe-assets/js/circle-progress.js",
      "/src/fe-assets/js/jquery-ui.min.js",
      "/src/fe-assets/js/imagesloaded.pkgd.min.js",
      "/src/fe-assets/js/isotope.pkgd.min.js",
      "/src/fe-assets/js/tilt.jquery.min.js",
      "/src/fe-assets/js/gsap.min.js",
      "/src/fe-assets/js/ScrollTrigger.min.js",
      "/src/fe-assets/js/smooth-scroll.js",
      "/src/fe-assets/js/particles.min.js",
      "/src/fe-assets/js/particles-config.js",
      "/src/fe-assets/js/main.js",
    ];

    const loadedScripts = [];

    scripts.forEach((src) => {
      const script = document.createElement("script");
      script.src = src;
      script.async = true;
      script.setAttribute("data-landing-js", "true"); // Đánh dấu để cleanup
      document.body.appendChild(script);
      loadedScripts.push(script);
    });

    // Cleanup function để remove scripts khi component unmount
    return () => {
      // Remove scripts
      loadedScripts.forEach((script) => {
        if (document.body.contains(script)) {
          document.body.removeChild(script);
        }
      });

      // Remove any remaining landing assets
      document
        .querySelectorAll("[data-landing-js]")
        .forEach((el) => el.remove());
    };
  }, []);

  return (
    <div className="landing-container">
      <LandingCSSLoader />
      <div className="cursor"></div>
      <div className="cursor2"></div>
      {children}

      {/* Back to top button */}
      <div className="scroll-top">
        <svg
          className="progress-circle svg-content"
          width="100%"
          height="100%"
          viewBox="-1 -1 102 102"
        >
          <path
            d="m50,1 a49,49 0 0,1 0,98 a49,49 0 0,1 0,-98"
            style={{
              transition: "stroke-dashoffset 10ms linear 0s",
              strokeDasharray: "307.919, 307.919",
              strokeDashoffset: "307.919",
            }}
          ></path>
        </svg>
      </div>
    </div>
  );
};

export default LandingLayout;
