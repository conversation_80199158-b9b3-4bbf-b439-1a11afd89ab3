// src/components/SuggestionInput.jsx

import React, { useState, useRef, useEffect } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";

/**
 * Input component với tính năng suggestion/autocomplete
 */
const SuggestionInput = ({
  value,
  onChange,
  suggestions = [],
  onSuggestionSelect,
  onInputChange,
  placeholder = "",
  className = "form-control",
  disabled = false,
  type = "text",
  required = false,
  ...props
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const inputRef = useRef(null);
  const suggestionRefs = useRef([]);

  // Handle input change
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    onChange(e);

    if (onInputChange) {
      onInputChange(newValue);
    }

    // Show suggestions nếu có
    if (suggestions.length > 0 && newValue.length >= 2) {
      setShowSuggestions(true);
      setSelectedIndex(-1);
    } else {
      setShowSuggestions(false);
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    const syntheticEvent = {
      target: { value: suggestion },
    };
    onChange(syntheticEvent);

    if (onSuggestionSelect) {
      onSuggestionSelect(suggestion);
    }

    setShowSuggestions(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  // Handle keyboard navigation
  const handleKeyDown = (e) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex((prev) =>
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;

      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex((prev) =>
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;

      case "Enter":
        e.preventDefault();
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          handleSuggestionClick(suggestions[selectedIndex]);
        }
        break;

      case "Escape":
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;

      default:
        break;
    }
  };

  // Handle input blur
  const handleBlur = () => {
    // Delay để cho phép click vào suggestion
    setTimeout(() => {
      setShowSuggestions(false);
      setSelectedIndex(-1);
    }, 150);
  };

  // Handle input focus
  const handleFocus = () => {
    if (suggestions.length > 0 && value && value.length >= 2) {
      setShowSuggestions(true);
    }
  };

  // Scroll selected suggestion into view
  useEffect(() => {
    if (selectedIndex >= 0 && suggestionRefs.current[selectedIndex]) {
      suggestionRefs.current[selectedIndex].scrollIntoView({
        block: "nearest",
        behavior: "smooth",
      });
    }
  }, [selectedIndex]);

  return (
    <div className="position-relative">
      <input
        ref={inputRef}
        type={type}
        className={className}
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onBlur={handleBlur}
        onFocus={handleFocus}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        autoComplete="off"
        {...props}
      />

      {showSuggestions && suggestions.length > 0 && (
        <div
          className="position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm"
          style={{
            zIndex: 1050,
            maxHeight: "200px",
            overflowY: "auto",
            top: "100%",
            left: 0,
            right: 0,
          }}
        >
          {suggestions.map((suggestion, index) => (
            <div
              key={index}
              ref={(el) => (suggestionRefs.current[index] = el)}
              className={`px-3 py-2 cursor-pointer border-bottom ${
                index === selectedIndex
                  ? "bg-primary text-white"
                  : "hover:bg-light"
              }`}
              style={{
                cursor: "pointer",
                backgroundColor:
                  index === selectedIndex ? "#0d6efd" : "transparent",
              }}
              onClick={() => handleSuggestionClick(suggestion)}
              onMouseEnter={() => setSelectedIndex(index)}
            >
              <div className="d-flex align-items-center">
                <Icon
                  icon="ph:clock-clockwise"
                  className="me-2 text-muted"
                  style={{ fontSize: "14px" }}
                />
                <span>{suggestion}</span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SuggestionInput;
