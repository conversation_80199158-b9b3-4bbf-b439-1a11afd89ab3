/* Brand 1 ---------------------------------- */
.brand-sec1 {
    position: relative;
    z-index: 4;
    border-radius: 10px;
    max-width: 1420px;
    margin-left: auto;
    margin-right: auto;
    &:after {
        content: '';
        position: absolute;
        inset: 0;
        background-color: $title-color;
        border-radius: inherit;
        clip-path: polygon(0 0, 100% 0, 100% 11px, calc(100% - 70px) 50%, 100% calc(100% - 11px), 100% 100%, 0 100%, 0 calc(100% - 11px), 70px 50%, 0 11px);
        z-index: -1;
        @include xxl {
            clip-path: polygon(0 0, 100% 0, 100% 11px, calc(100% - 40px) 50%, 100% calc(100% - 11px), 100% 100%, 0 100%, 0 calc(100% - 11px), 40px 50%, 0 11px);
        }
        @include xl {
            clip-path: polygon(0 0, 100% 0, 100% 11px, calc(100% - 30px) 50%, 100% calc(100% - 11px), 100% 100%, 0 100%, 0 calc(100% - 11px), 30px 50%, 0 11px);
        }
    }
    @include sm {
        margin-left: 12px;
        margin-right: 12px;
        &:after {
            clip-path: none;
        }
    }
    .slick-arrow {
        visibility: visible;
        opacity: 1;
    }
}

@include sm {
    .brand-box.py-20 {
        padding-top: 0;
        padding-bottom: 0;
    }
}

/* Brand 1.1 ---------------------------------- */
.brand-box1-1 {
    border-radius: 20px;
    border: 1px solid #EDF0F4;
    background: linear-gradient(180deg, #EDF0F4 0%, rgba(237, 240, 244, 0.00) 100%);
    display: grid;
    align-content: center;
    text-align: center;
    justify-content: center;
    min-height: 90px;
    // filter: grayscale(1) invert(0.3) brightness(1.4);
    transition: 0.4s ease-in-out;

    img {
        transition: all 0.4s ease-in-out;
        transform: scale(0.9);
    }

    &:hover {
        filter: none;

        img {
            transform: scale(1);
        }
    }
}

.brand-title {
    letter-spacing: -0.4px; 
    font-weight: 600;
}

/* Brand 4 ---------------------------------- */
.brand-slider4 {
    padding-bottom: 60px;
    border-bottom: 1px solid #D3D3E7;
}