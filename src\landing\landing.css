/*
  Landing Page CSS - Scoped với .landing-container
  Import CSS từ fe-assets và chỉ áp dụng cho landing page
*/

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap');

/* FontAwesome từ CDN */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css');

/* Container chính cho landing page */
.landing-container {
  font-family: 'Nunito', sans-serif;
  position: relative;
  width: 100%;
  min-height: 100vh;
}

/* Reset styles chỉ trong landing container */
.landing-container *,
.landing-container *::before,
.landing-container *::after {
  box-sizing: border-box;
}

/* Import và scope CSS từ fe-assets */
.landing-container {
  /* Sẽ load CSS từ fe-assets thông qua link tags trong head */
}

/* Cursor effects chỉ trong landing */
.landing-container .cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ff6b35;
  pointer-events: none;
  z-index: 9999;
  transition: transform 0.1s ease;
}

.landing-container .cursor2 {
  position: fixed;
  width: 40px;
  height: 40px;
  border: 2px solid #ff6b35;
  border-radius: 50%;
  pointer-events: none;
  z-index: 9998;
  transition: transform 0.1s ease;
}

/* Back to top button */
.landing-container .scroll-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 999;
  transition: all 0.3s ease;
}

.landing-container .scroll-top:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
}