import React, { useEffect } from 'react';

const LandingCSSLoader = () => {
  useEffect(() => {
    // Function để thêm prefix cho CSS rules
    const addPrefixToCSS = (cssText, prefix = '.landing-container') => {
      // Regex để match CSS selectors
      return cssText.replace(/([^{}]+)\s*{/g, (match, selector) => {
        // Bỏ qua @rules như @media, @keyframes, @import
        if (selector.trim().startsWith('@')) {
          return match;
        }
        
        // Split multiple selectors separated by comma
        const selectors = selector.split(',').map(s => {
          const trimmed = s.trim();
          
          // Special cases
          if (trimmed === 'html' || trimmed === 'body' || trimmed === '*') {
            return `${prefix} ${trimmed}`;
          }
          
          if (trimmed.startsWith(':root')) {
            return trimmed; // Keep :root as is
          }
          
          // Add prefix to normal selectors
          return `${prefix} ${trimmed}`;
        }).join(', ');
        
        return `${selectors} {`;
      });
    };

    // Load và process CSS files
    const loadAndProcessCSS = async () => {
      const cssFiles = [
        '/src/fe-assets/css/bootstrap.min.css',
        '/src/fe-assets/css/fontawesome.min.css',
        '/src/fe-assets/css/magnific-popup.min.css',
        '/src/fe-assets/css/swiper-bundle.min.css',
        '/src/fe-assets/css/style.css'
      ];

      try {
        for (const cssFile of cssFiles) {
          const response = await fetch(cssFile);
          if (response.ok) {
            const cssText = await response.text();
            const prefixedCSS = addPrefixToCSS(cssText);
            
            // Create style element
            const style = document.createElement('style');
            style.setAttribute('data-landing-css-processed', cssFile);
            style.textContent = prefixedCSS;
            document.head.appendChild(style);
          }
        }
      } catch (error) {
        console.warn('Error loading CSS files:', error);
        // Fallback: load CSS files normally
        const cssFiles = [
          '/src/fe-assets/css/bootstrap.min.css',
          '/src/fe-assets/css/fontawesome.min.css',
          '/src/fe-assets/css/magnific-popup.min.css',
          '/src/fe-assets/css/swiper-bundle.min.css',
          '/src/fe-assets/css/style.css'
        ];

        cssFiles.forEach(href => {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = href;
          link.setAttribute('data-landing-css-fallback', 'true');
          document.head.appendChild(link);
        });
      }
    };

    loadAndProcessCSS();

    // Cleanup function
    return () => {
      // Remove processed CSS
      document.querySelectorAll('[data-landing-css-processed]').forEach(el => el.remove());
      document.querySelectorAll('[data-landing-css-fallback]').forEach(el => el.remove());
    };
  }, []);

  return null; // This component doesn't render anything
};

export default LandingCSSLoader;
