import React from "react";
import heroBg from "../assets/landing/img/hero/hero_bg_7_1.png";
import heroImg from "../assets/landing/img/hero/hero_img_7_1.png";

const Hero = () => {
  return (
    <div className="th-hero-wrapper hero-7" id="hero">
      <div className="hero-inner">
        <div
          className="th-hero-bg"
          style={{ backgroundImage: `url(${heroBg})` }}
        />
        <div className="container th-container4">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              <div className="hero-style7 text-center">
                <h1 className="hero-title">
                  G<PERSON><PERSON><PERSON>p <PERSON>án{" "}
                  <span className="text-theme">Tự Động</span> <br />
                  Biến Động Số Dư
                </h1>
                <p className="hero-text">
                  G<PERSON><PERSON><PERSON> pháp thanh toán tự động, Open Banking Pay2S là đối tác
                  liên kết trực tiế<PERSON> v<PERSON><PERSON> ng<PERSON>, an toàn - bảo mật - <PERSON><PERSON> đ<PERSON>nh
                </p>
                <div className="btn-group mt-35 justify-content-center">
                  <a
                    href="https://pay2s.vn/client/"
                    className="th-btn style-radius"
                  >
                    Đăng ký miễn phí ngay
                  </a>
                  <a href="/lien-he" className="th-btn style6 style-radius">
                    Liên hệ tư vấn
                  </a>
                </div>
              </div>
            </div>
          </div>
          <div className="th-hero-thumb">
            <img src={heroImg} alt="img" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Hero;
