// src/client/addbankform/BidvPersonalOpenApiForm.jsx

import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import useBankApi from "../../callapi/Bank.jsx";
import { navigateToAccountList } from "../../utils/bankUtils.js";
import useFormSuggestions from "../../hooks/useFormSuggestions.js";
import SuggestionInput from "../../components/SuggestionInput.jsx";

const BidvPersonalOpenApiForm = ({ bankName }) => {
  // --- State cho dữ liệu form ---
  const [accName, setAccName] = useState("");
  const [accEmail, setAccEmail] = useState("");
  const [accMobile, setAccMobile] = useState("");
  const [cccd, setCccd] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [merchantId, setMerchantId] = useState("");
  const [agree, setAgree] = useState(false);
  const [showOtpInput, setShowOtpInput] = useState(false);
  const [otp, setOtp] = useState("");
  const [lastProcessedResponse, setLastProcessedResponse] = useState(null);

  // --- State quản lý thông báo (gộp chung) ---
  const [notification, setNotification] = useState({
    error: null,
    success: null,
  });

  // --- Hooks cho API và điều hướng ---
  const {
    data: apiResponse,
    loading: isLoading,
    error: apiError,
    callApi,
  } = useBankApi();
  const navigate = useNavigate();

  // Hook để quản lý suggestions
  const {
    activeSuggestions,
    setFieldSuggestions,
    clearFieldSuggestions,
    submitFormData,
  } = useFormSuggestions(`bidv_personal_${bankName}`);

  // console.log({ bankName });
  /**
   * Hàm xử lý chính khi người dùng nhấn nút submit.
   */
  const handleSubmit = async (event) => {
    event.preventDefault();
    setNotification({ error: null, success: null });
    const userId = localStorage.getItem("user_id");

    if (!userId) {
      setNotification({
        error: "Lỗi xác thực: Không tìm thấy User ID. Vui lòng đăng nhập lại.",
        success: null,
      });
      return;
    }

    let apiBody;
    if (showOtpInput) {
      // Giai đoạn 2: Gửi thông tin kèm mã OTP để xác nhận
      if (!otp || otp.trim().length < 4) {
        setNotification({
          error: "Vui lòng nhập mã OTP hợp lệ.",
          success: null,
        });
        return;
      }
      apiBody = {
        accName,
        accountNumber,
        accEmail,
        merchantId,
        shortName: bankName,
        action: "confirm_otp",
        user_id: userId,
        cccd,
        accMobile,
        type: "openapi",
        otp,
      };
    } else {
      // Giai đoạn 1: Gửi thông tin tài khoản ban đầu
      if (!agree) {
        setNotification({
          error: "Bạn phải đồng ý với điều khoản và điều kiện.",
          success: null,
        });
        return;
      }
      apiBody = {
        accName,
        accountNumber,
        accEmail,
        merchantId,
        shortName: bankName,
        action: "add",
        user_id: userId,
        cccd,
        accMobile,
        type: "openapi",
      };
    }

    try {
      console.log("[BIDV] Submit body:", apiBody);
      await callApi(apiBody);
    } catch (err) {
      setNotification({
        error: "Lỗi gửi yêu cầu. Vui lòng thử lại hoặc kiểm tra kết nối.",
        success: null,
      });
      console.error("[BIDV] Submit error:", err);
    }
  };

  useEffect(() => {
    // Tránh xử lý lại cùng một response
    if (apiResponse && apiResponse === lastProcessedResponse) {
      return;
    }

    if (apiError) {
      setNotification({ error: apiError, success: null });
      return;
    }

    if (apiResponse && apiResponse.status === true) {
      setLastProcessedResponse(apiResponse);
      if ((apiResponse.OTP === 1 || apiResponse.otp === 1) && !showOtpInput) {
        setShowOtpInput(true);
        setNotification({
          success: "Gửi OTP Thành công. Vui lòng nhập mã OTP để hoàn tất.",
          error: null,
        });
        return;
      }
      // Không cần OTP hoặc đã xác nhận OTP thành công
      if (
        (apiResponse.OTP !== 1 && apiResponse.otp !== 1) ||
        ((apiResponse.OTP === 1 || apiResponse.otp === 1) && showOtpInput)
      ) {
        const finalMessage = apiResponse.message || "Thao tác thành công!";
        setNotification({
          success: `${finalMessage} Đang chuyển hướng...`,
          error: null,
        });
        submitFormData({
          accName,
          accEmail,
          accMobile,
          cccd,
          accountNumber,
          merchantId,
        });
        navigateToAccountList(navigate, bankName);
      }
    } else if (apiResponse && apiResponse.status === false) {
      setNotification({
        error: apiResponse.message || "Có lỗi xảy ra khi xử lý yêu cầu",
        success: null,
      });
      setLastProcessedResponse(apiResponse);
    }
  }, [
    apiResponse,
    apiError,
    navigate,
    bankName,
    showOtpInput,
    lastProcessedResponse,
  ]);

  return (
    <form onSubmit={handleSubmit}>
      {/* Khu vực hiển thị thông báo đã được gộp lại */}
      {notification.error && (
        <div className="alert alert-danger">{notification.error}</div>
      )}
      {notification.success && (
        <div className="alert alert-success">{notification.success}</div>
      )}

      {/* Các trường input */}
      <div className="mt-20">
        <label className="form-label">Họ và tên *</label>
        <SuggestionInput
          className="form-control"
          type="text"
          value={accName}
          onChange={(e) => setAccName(e.target.value.toUpperCase())}
          suggestions={activeSuggestions.accName || []}
          onInputChange={(value) => setFieldSuggestions("accName", value)}
          onSuggestionSelect={(value) => {
            setAccName(value.toUpperCase());
            clearFieldSuggestions("accName");
          }}
          style={{ textTransform: "uppercase" }}
          placeholder="Nhập họ và tên..."
          required
          disabled={isLoading}
        />
      </div>

      <div className="mt-20">
        <label className="form-label">Email *</label>
        <SuggestionInput
          className="form-control"
          type="email"
          value={accEmail}
          onChange={(e) => setAccEmail(e.target.value)}
          suggestions={activeSuggestions.accEmail || []}
          onInputChange={(value) => setFieldSuggestions("accEmail", value)}
          onSuggestionSelect={(value) => {
            setAccEmail(value);
            clearFieldSuggestions("accEmail");
          }}
          placeholder="Nhập email..."
          required
          disabled={isLoading}
        />
      </div>

      <div className="mt-20">
        <label className="form-label">Số điện thoại *</label>
        <SuggestionInput
          className="form-control"
          type="tel"
          value={accMobile}
          onChange={(e) => setAccMobile(e.target.value)}
          suggestions={activeSuggestions.accMobile || []}
          onInputChange={(value) => setFieldSuggestions("accMobile", value)}
          onSuggestionSelect={(value) => {
            setAccMobile(value);
            clearFieldSuggestions("accMobile");
          }}
          placeholder="Nhập số điện thoại..."
          required
          disabled={isLoading}
        />
      </div>

      <div className="mt-20">
        <label className="form-label">Số căn cước công dân *</label>
        <SuggestionInput
          className="form-control"
          type="text"
          value={cccd}
          onChange={(e) => setCccd(e.target.value)}
          suggestions={activeSuggestions.cccd || []}
          onInputChange={(value) => setFieldSuggestions("cccd", value)}
          onSuggestionSelect={(value) => {
            setCccd(value);
            clearFieldSuggestions("cccd");
          }}
          placeholder="Nhập số CCCD..."
          required
          disabled={isLoading}
        />
      </div>

      <div className="mt-20">
        <label className="form-label">Số tài khoản {bankName} *</label>
        <SuggestionInput
          className="form-control"
          type="text"
          value={accountNumber}
          onChange={(e) => setAccountNumber(e.target.value)}
          suggestions={activeSuggestions.accountNumber || []}
          onInputChange={(value) => setFieldSuggestions("accountNumber", value)}
          onSuggestionSelect={(value) => {
            setAccountNumber(value);
            clearFieldSuggestions("accountNumber");
          }}
          placeholder="Nhập số tài khoản..."
          required
          disabled={isLoading}
        />
      </div>
      <div className="mt-20">
        <label className="form-label">Tạo số tài khoản ảo *</label>
        <div className="input-group position-relative">
          <span className="input-group-text" id="basic-addon1">
            963869
          </span>
          <SuggestionInput
            className="form-control"
            type="text"
            value={merchantId}
            onChange={(e) => setMerchantId(e.target.value.toUpperCase())}
            suggestions={activeSuggestions.merchantId || []}
            onInputChange={(value) => setFieldSuggestions("merchantId", value)}
            onSuggestionSelect={(value) => {
              setMerchantId(value.toUpperCase());
              clearFieldSuggestions("merchantId");
            }}
            required
            disabled={isLoading}
            placeholder="Nhập số hoặc chữ (ví dụ: VYNT)"
          />
        </div>
      </div>

      {/* Trường nhập OTP */}
      {showOtpInput && (
        <div className="mt-20">
          <label className="form-label fw-bold text-danger">Mã OTP *</label>
          <input
            className="form-control"
            type="text"
            value={otp}
            onChange={(e) => setOtp(e.target.value)}
            required
            placeholder="Nhập mã OTP đã được gửi đến bạn"
            autoFocus
          />
        </div>
      )}

      {/* Checkbox điều khoản */}
      {!showOtpInput && (
        <div className="form-check mt-20">
          <input
            className="form-check-input"
            type="checkbox"
            id="agreePay2s"
            checked={agree}
            onChange={(e) => setAgree(e.target.checked)}
            required
          />
          <label className="form-check-label small" htmlFor="agreePay2s">
            Bằng cách cung cấp thông tin cho Pay2S. Bạn đã đồng ý với{" "}
            <a
              href="https://bidv.com.vn/uudai/DKDKDV_VIETQR_FUTE_081024.pdf"
              className="text-primary-600 fw-semibold"
              target="_blank"
              rel="noopener noreferrer"
            >
              Chính sách bảo mật *
            </a>{" "}
            của Pay2S và cho phép Pay2S truy xuất thông tin tài chính từ ngân
            hàng của bạn và Đồng ý nhận thông báo tiền về từ ngân hàng đến hệ
            thống Pay2S.
          </label>
        </div>
      )}

      {/* Nút Submit */}
      <div className="mt-20">
        <button
          type="submit"
          className="btn btn-success"
          disabled={isLoading || (showOtpInput && !otp)}
        >
          {isLoading
            ? showOtpInput
              ? "ĐANG XÁC NHẬN OTP..."
              : "ĐANG XỬ LÝ..."
            : showOtpInput
            ? "XÁC NHẬN OTP"
            : "THÊM TÀI KHOẢN"}
        </button>
      </div>
    </form>
  );
};

export default BidvPersonalOpenApiForm;
