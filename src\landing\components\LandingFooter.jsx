import React from 'react';

const LandingFooter = () => {
  return (
    <>
      {/* Footer Area */}
      <footer
        className="footer-wrapper footer-layout6"
        data-bg-src="/src/fe-assets/img/bg/footer_bg_6.jpg"
      >
        <div className="container th-container4">
          <div className="widget-area">
            <div className="row justify-content-between">
              <div className="col-md-6 col-xl-4">
                <div className="widget footer-widget">
                  <div className="header-logo">
                    <a className="icon-masking" href="/">
                      <img
                        src="/src/fe-assets/img/logo.png"
                        alt="pay2s"
                        className="pay2s-logo"
                      />
                    </a>
                  </div>
                  <h3 className="widget_title mt-2"></h3>
                  <div className="info-widget">
                    <strong>CÔNG TY CỔ PHẦN FUTE</strong>
                    <div className="footer-text small">
                      <i className="fa-solid fa-house"></i> 15/40/30 <PERSON><PERSON><PERSON><PERSON> số 59,
                      <PERSON><PERSON><PERSON><PERSON> 14, <PERSON><PERSON><PERSON>, TP. HCM <br />
                      <i className="fa-solid fa-phone"></i> 028 627 05478<br />
                      <i className="fa-solid fa-envelope"></i> <EMAIL><br />
                      <i className="fa-brands fa-square-facebook"></i>
                      facebook.com/pay2s<br />
                      <i className="fa-solid fa-folder-open"></i> MST: 0318057907<br />
                    </div>
                  </div>
                  <div className="newsletter-widget">
                    <form className="newsletter-form">
                      <i className="fa-sharp fa-light fa-envelope"></i>
                      <input
                        className="form-control"
                        type="email"
                        placeholder="Email Address"
                        required
                      />
                      <button type="submit" className="th-btn">Subscribe</button>
                    </form>
                  </div>
                </div>
              </div>
              <div className="col-md-6 col-xl-auto">
                <div className="widget footer-widget">
                  <h3 className="widget_title">Ngân hàng đối tác</h3>
                  <div className="banking-coop">
                    <div className="row mt-3 mb-3">
                      <div className="col-4">
                        <img src="/src/fe-assets/img/bank/ft_1.png" alt="" />
                      </div>
                      <div className="col-4">
                        <img src="/src/fe-assets/img/bank/ft_2.png" alt="" />
                      </div>
                      <div className="col-4">
                        <img src="/src/fe-assets/img/bank/ft_3.png" alt="" />
                      </div>
                    </div>
                    <div className="row mt-3 mb-3">
                      <div className="col-4">
                        <img src="/src/fe-assets/img/bank/ft_5.png" alt="" />
                      </div>
                      <div className="col-4">
                        <img src="/src/fe-assets/img/bank/ft_6.png" alt="" />
                      </div>
                      <div className="col-4">
                        <img src="/src/fe-assets/img/bank/ft_7.png" alt="" />
                      </div>
                    </div>
                    <div className="row mt-3 mb-3">
                      <div className="col-4"></div>
                      <div className="col-4">
                        <img src="/src/fe-assets/img/bank/ft_4.png" alt="" />
                      </div>
                      <div className="col-4"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-md-6 col-xl-auto">
                <div className="widget widget_nav_menu footer-widget">
                  <h3 className="widget_title">Sản phẩm</h3>
                  <div className="menu-all-pages-container">
                    <ul className="menu">
                      <li><a href="/open-api-banking">Open Banking</a></li>
                      <li><a href="/cong-thanh-toan">API Thanh toán tự động</a></li>
                      <li><a href="/chia-se-bien-dong-so-du">Chia sẽ biến động số dư</a></li>
                      <li><a href="/cong-thanh-toan#sec-title">Cổng thanh toán WHMCS</a></li>
                      <li><a href="/cong-thanh-toan#sec-title">Cổng thanh toán Hostbill</a></li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="col-md-6 col-xl-auto">
                <div className="widget widget_nav_menu footer-widget">
                  <h3 className="widget_title">Công ty</h3>
                  <div className="menu-all-pages-container">
                    <ul className="menu">
                      <li><a href="/about">Về Pay2S</a></li>
                      <li><a href="/bang-gia">Bảng giá</a></li>
                      <li><a href="https://pay2s.vn/tin-tuc">Tin tức</a></li>
                      <li><a href="https://docs.pay2s.vn/">Tài liệu</a></li>
                    </ul>
                  </div>
                </div>
              </div>
              <div className="col-md-6 col-xl-auto">
                <div className="widget widget_nav_menu footer-widget">
                  <h3 className="widget_title">Thông tin</h3>
                  <div className="menu-all-pages-container">
                    <ul className="menu">
                      <li><a href="/open-api-banking#team-sec">Công bố hợp tác</a></li>
                      <li><a href="/chinh-sach-bao-mat">Chính sách bảo mật</a></li>
                      <li><a href="/thoa-thuan">Thỏa thuận sử dụng dịch vụ</a></li>
                      <li>
                        <a href="/tiep-nhan-xu-ly">Tiếp nhận & Xử lý khiếu nại</a>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="container th-container4">
          <div className="copyright-wrap">
            <div className="row justify-content-between align-items-center">
              <div className="col-lg-6">
                <p className="copyright-text">
                  Copyright <i className="fal fa-copyright"></i>
                  <a href="https://pay2s.vn">Pay2S </a>là chủ sở hữu và có toàn
                  quyền tác giả phần mềm Pay2S .
                </p>
              </div>
              <div className="col-lg-6 text-end d-none d-lg-block">
                <div className="footer-links">
                  <a href="/chinh-sach-bao-mat">Chính sách bảo mật</a>
                  <a href="/thoa-thuan">Thỏa thuận sử dụng</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </>
  );
};

export default LandingFooter;
