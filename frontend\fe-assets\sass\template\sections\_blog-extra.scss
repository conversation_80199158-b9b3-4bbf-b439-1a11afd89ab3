/* Blog Card ---------------------------------- */
.blog-card {
    box-shadow: 0px 10px 15px rgba(8, 14, 28, 0.06);
    border-radius: 10px;
    background-color: $white-color;
    .box-title {
        margin-bottom: 22px;
        a {
            background-image: linear-gradient(to left, $theme-color, $theme-color);
            background-repeat: no-repeat;
            background-position: bottom left;
            background-size: 0 2px;
            transition: 0.5s ease-in-out;
            &:hover {
                background-size: 100% 2px;
            }
        }
    }
    .blog-img {
        overflow: hidden;
        box-shadow: 0px 10px 15px rgba(23, 27, 42, 0.06);
        border-radius: 10px 10px 0px 0px;
        img {
            width: 100%;
            transition: 0.4s ease-in-out;
        }
    }
    .blog-meta {
        margin-bottom: 20px;
        span, 
        a {
            > i {
                color: inherit;
            }
        }
    }
    .blog-content {
        padding: 30px 40px;
    }
    .blog-bottom {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid $border-color;
        padding-top: 25px;
    }
    .author {
        color: $body-color;
        img {
            margin-right: 5px;
        }
        &:hover {
            color: $theme-color;
        }
    }
    .blog-text {
        margin-top: -0.5em;
        margin-bottom: 22px;
    }
    .line-btn {
        margin-bottom: 0;
        display: block;
        width: fit-content;
        color: $body-color;
        &:before {
            background-color: $body-color;
        }
        &:hover {
            color: $theme-color;
            &:before {
                background-color: $theme-color;
            }
        }
    }
    &:hover {
        .blog-img {
            img {
                transform: scale(1.08);
            }
        }
    }
}

@include xl {
    .blog-card .blog-content {
        padding: 30px 35px;
    }
}

@include md {
    .blog-card {
        .blog-content {
            padding: 30px 30px;
        }
        .box-title {
            font-size: 22px;
        }
    }
}

@include sm {
    .blog-card {
        .blog-content {
            padding: 30px 40px;
        }
        .box-title {
            font-size: 24px;
        }
    }
}
@media (max-width: 410px) {
    .blog-card {
        .blog-content {
            padding: 30px 30px;
        }
        .box-title {
            font-size: 22px;
        }
    }
}

@media (max-width: 350px) {
    .blog-card {
        .blog-content {
            padding: 30px 20px;
        }
        .box-title {
            font-size: 20px;
        }
    }
}