import React from "react";
import footerBg from "../assets/landing/img/bg/footer_bg_6.jpg";
import logoSrc from "../assets/landing/img/logo.png";
import bank1 from "../assets/landing/img/bank/ft_1.png";
import bank2 from "../assets/landing/img/bank/ft_2.png";
import bank3 from "../assets/landing/img/bank/ft_3.png";
import bank7 from "../assets/landing/img/bank/ft_7.png";
import bank5 from "../assets/landing/img/bank/ft_5.png";
import bank6 from "../assets/landing/img/bank/ft_6.png";

const Footer = () => {
  return (
    <footer
      className="footer-wrapper footer-layout6"
      style={{
        backgroundImage: `url(${footerBg})`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      <div className="container th-container4">
        <div className="widget-area">
          <div className="row justify-content-between">
            <div className="col-md-6 col-xl-4">
              <div className="widget footer-widget">
                <div className="header-logo">
                  <a className="icon-masking" href="/">
                    <img src={logoSrc} alt="pay2s" className="pay2s-logo" />
                  </a>
                </div>
                <h3 className="widget_title mt-2"></h3>
                <div className="info-widget">
                  <strong>CÔNG TY CỔ PHẦN FUTE</strong>
                  <div className="footer-text small">
                    <i className="fa-solid fa-house"></i> 15/40/30 Đường số 59,
                    Phường 14, Q.Gò Vấp, TP. HCM <br />
                    <i className="fa-solid fa-phone"></i> 028 627 05478
                    <br />
                    <i className="fa-solid fa-envelope"></i> <EMAIL>
                    <br />
                    <i className="fa-brands fa-square-facebook"></i>{" "}
                    facebook.com/pay2s
                    <br />
                    <i className="fa-solid fa-folder-open"></i> MST: 0318057907
                    <br />
                  </div>
                </div>
                <div className="newsletter-widget">
                  <form className="newsletter-form">
                    <i className="fa-sharp fa-light fa-envelope"></i>
                    <input
                      className="form-control"
                      type="email"
                      placeholder="Email Address"
                      required
                    />
                    <button type="submit" className="th-btn">
                      Subscribe
                    </button>
                  </form>
                </div>
              </div>
            </div>
            <div className="col-md-6 col-xl-auto">
              <div className="widget footer-widget">
                <h3 className="widget_title">Ngân hàng đối tác</h3>
                <div className="banking-coop">
                  <div className="row mt-3 mb-3">
                    <div className="col-4">
                      <img src={bank1} alt="Bank 1" />
                    </div>
                    <div className="col-4">
                      <img src={bank2} alt="Bank 2" />
                    </div>
                    <div className="col-4">
                      <img src={bank3} alt="Bank 3" />
                    </div>
                  </div>
                  <div className="row mt-3 mb-3">
                    <div className="col-4">
                      <img src={bank5} alt="Bank 5" />
                    </div>
                    <div className="col-4">
                      <img src={bank6} alt="Bank 6" />
                    </div>
                    <div className="col-4">
                      <img src={bank7} alt="Bank 7" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-md-6 col-xl-auto">
              <div className="widget widget_nav_menu footer-widget">
                <h3 className="widget_title">Sản phẩm</h3>
                <div className="menu-all-pages-container">
                  <ul className="menu">
                    <li>
                      <a href="/open-api-banking">Open Banking</a>
                    </li>
                    <li>
                      <a href="/cong-thanh-toan">API Thanh toán tự động</a>
                    </li>
                    <li>
                      <a href="/chia-se-bien-dong-so-du">
                        Chia sẽ biến động số dư
                      </a>
                    </li>
                    <li>
                      <a href="/cong-thanh-toan#sec-title">
                        Cổng thanh toán WHMCS
                      </a>
                    </li>
                    <li>
                      <a href="/cong-thanh-toan#sec-title">
                        Cổng thanh toán Hostbill
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="col-md-6 col-xl-auto">
              <div className="widget widget_nav_menu footer-widget">
                <h3 className="widget_title">Công ty</h3>
                <div className="menu-all-pages-container">
                  <ul className="menu">
                    <li>
                      <a href="/about">Về Pay2S</a>
                    </li>
                    <li>
                      <a href="/bang-gia">Bảng giá</a>
                    </li>
                    <li>
                      <a href="https://pay2s.vn/tin-tuc">Tin tức</a>
                    </li>
                    <li>
                      <a href="https://docs.pay2s.vn/">Tài liệu</a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="col-md-6 col-xl-auto">
              <div className="widget widget_nav_menu footer-widget">
                <h3 className="widget_title">Thông tin</h3>
                <div className="menu-all-pages-container">
                  <ul className="menu">
                    <li>
                      <a href="/open-api-banking#team-sec">Công bố hợp tác</a>
                    </li>
                    <li>
                      <a href="/chinh-sach-bao-mat">Chính sách bảo mật</a>
                    </li>
                    <li>
                      <a href="/thoa-thuan">Thỏa thuận sử dụng dịch vụ</a>
                    </li>
                    <li>
                      <a href="/tiep-nhan-xu-ly">Tiếp nhận & Xử lý khiếu nại</a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="copyright-wrap">
          <div className="row justify-content-between align-items-center">
            <div className="col-lg-6">
              <p className="copyright-text">
                Copyright <i className="fal fa-copyright"></i>
                <a href="https://pay2s.vn">Pay2S </a>là chủ sở hữu và có toàn
                quyền tác giả phần mềm Pay2S .
              </p>
            </div>
            <div className="col-lg-6 text-lg-end text-center">
              <div className="th-social style4">
                <a href="https://www.facebook.com/">
                  <i className="fab fa-facebook-f"></i>
                </a>
                <a href="https://www.twitter.com/">
                  <i className="fab fa-twitter"></i>
                </a>
                <a href="https://www.linkedin.com/">
                  <i className="fab fa-linkedin-in"></i>
                </a>
                <a href="https://www.whatsapp.com/">
                  <i className="fab fa-whatsapp"></i>
                </a>
                <a href="https://www.youtube.com/">
                  <i className="fab fa-youtube"></i>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
